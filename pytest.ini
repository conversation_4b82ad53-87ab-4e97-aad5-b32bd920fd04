[pytest]
env =
    APP_ENV=testing
    DB_USER=postgres
    DB_PASSWORD=postgres
    DB_HOST=localhost
    DB_PORT=5432
    DB_NAME=interface_exchange_test
    DB_SCHEMA=public
    RABBITMQ_URL=amqp://guest:guest@localhost:5672/
    DEFAULT_TENANT_ID=treebo

# Test discovery
testpaths = interface_exchange/integration_tests/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Pytest options
addopts =
    --strict-markers
    --strict-config
    --verbose

# Custom markers
markers =
    regression: marks tests as regression tests
    smoke: marks tests as smoke tests
    integration: marks tests as integration tests
    slow: marks tests as slow running

# Minimum version
minversion = 6.0

# Ignore certain warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
