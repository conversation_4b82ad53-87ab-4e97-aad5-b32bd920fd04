version: '2.1'
services:
  app:
    extends:
      file: docker-base.yml
      service: app
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}

  catalouge_rmq_consumers_hotels:
    extends:
      file: docker-base.yml
      service: catalouge_rmq_consumers_hotels
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}

  catalouge_rmq_consumers_amenities:
    extends:
      file: docker-base.yml
      service: catalouge_rmq_consumers_amenities
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}

  catalouge_rmq_consumers_rooms:
    extends:
      file: docker-base.yml
      service: catalouge_rmq_consumers_rooms
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}

  catalogue_service_consumer_property_media:
    extends:
      file: docker-base.yml
      service: catalogue_service_consumer_property_media
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}

  job_consumer:
    extends:
      file: docker-base.yml
      service: job_consumer
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}
    restart: always

  job_scheduler:
    extends:
      file: docker-base.yml
      service: job_scheduler
    environment:
      - HOST_NAME=${HOST_NAME}
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}
    restart: always

  treebo_cron_service:
    extends:
      service: treebo_cron_service
      file: docker-base.yml
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}
    restart: always

  nginx_service:
    image: "nginx"
    restart : always
    depends_on:
      app:
        condition: service_healthy
    volumes:
      - ${CONFIG_MOUNT_PATH_NGINX_CONF}
      - ${CONFIG_MOUNT_PATH_NGINX_SITES_ENABLED}
      - ${CONFIG_MOUNT_PATH_NGINX_LOG_ROOT}
      - ${MOUNT_HOST_STATIC_PIPELINE_NGINX}
    network_mode: "host"
    container_name: "nginx-tools-production-new"
    ports:
      - "90:90"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - APP_ENV=${APP_ENV}

