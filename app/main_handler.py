import json

from app.core.sentry import init_sentry
from app.core.logging import configure_logging

init_sentry()  # Init once at cold start
configure_logging()  # Configure logging at cold start

import asyncio
from app.controllers.event_controller import EventController


async def _handler(event, context):
    if "Records" in event and event["Records"][0].get("EventSource") == "aws:sns":
        for record in event["Records"]:
            message = json.loads(record["Sns"]["Message"])
            await EventController().handle_sns_event(message)
        return {"statusCode": 200, "body": json.dumps({"message": "SNS processed"})}

    if "httpMethod" in event:
        response = await EventController().handle_api_event(event)
        return {"statusCode": 200, "body": json.dumps(response)}

    return {"statusCode": 400, "body": json.dumps({"error": "Unknown event type"})}


# Create a persistent event loop
_loop = None


def get_event_loop():
    global _loop
    if _loop is None:
        _loop = asyncio.new_event_loop()
        asyncio.set_event_loop(_loop)
    return _loop


def handler(event, context):
    loop = get_event_loop()
    return loop.run_until_complete(_handler(event, context))


if __name__ == "__main__":
    import asyncio
    import uuid

    events = [
        {
            "Records": [
                {
                    "EventSource": "aws:sns",
                    "Sns": {
                        "Message": json.dumps(
                            {
                                "tenant_id": "treebo",
                                "property_id": "0001825",
                                "request_id": str(uuid.uuid4()),
                                "method": "GUEST_CHECKIN",
                                "room_number": "101",
                                "guest_last_name": "Doe",
                                "guest_first_name": "John",
                            }
                        )
                    },
                }
            ]
        },
        {
            "Records": [
                {
                    "EventSource": "aws:sns",
                    "Sns": {
                        "Message": json.dumps(
                            {
                                "tenant_id": "treebo",
                                "property_id": "0001825",
                                "request_id": str(uuid.uuid4()),
                                "method": "GUEST_CHECKOUT",
                                "room_number": "101",
                                "guest_last_name": "Doe",
                                "guest_first_name": "John",
                            }
                        )
                    },
                }
            ]
        },
    ]

    async def process_events():
        tasks = []
        for event in events:
            task = asyncio.create_task(_handler(event, None))
            tasks.append(task)
        await asyncio.gather(*tasks)

    asyncio.run(process_events())
