from flask_httpauth import <PERSON><PERSON><PERSON><PERSON>asic<PERSON>uth
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from werkzeug.security import generate_password_hash, check_password_hash

auth = HTTPBasicAuth()
basic_auth_creds = AwsSecretManager.get_basic_auth_creds()
users = {
    basic_auth_creds['username']: generate_password_hash(basic_auth_creds['password'])
}


@auth.verify_password
def verify_password(username, password):
    if username in users and \
            check_password_hash(users.get(username), password):
        return username
