from typing import List

from interface_exchange.exceptions import ResourceNotFound
from object_registry import register_instance
from interface_exchange.domain.entities.aggregate_roots.interface import Interface
from interface_exchange.infrastructure.models.interface import InterfaceModel
from interface_exchange.infrastructure.repositories.adaptors.interface_adaptor import \
    InterfaceAdaptor
from interface_exchange.infrastructure.repositories.base_repository import \
    BaseRepository


@register_instance()
class InterfaceRepo(BaseRepository):
    _model = InterfaceModel
    _adaptor = InterfaceAdaptor

    def create_interface(self, property_id, name, interface_physical_id, interface_type,
                         hardware_provider, protocol, network, configs, interface_sub_type=None):
        interface_model = self._model(property_id=property_id, name=name, interface_physical_id=interface_physical_id,
                                      interface_type=interface_type, hardware_provider=hardware_provider,
                                      protocol=protocol, network=network, configs=configs, interface_sub_type=interface_sub_type)
        self.create(interface_model)
        return self._adaptor.to_domain_entity(interface_model)

    def load_interfaces(self, property_id, search_params) -> List[Interface]:
        query = self.session().query(self._model) \
            .filter(self._model.property_id == property_id).order_by(self._model.created_at.desc())

        if search_params.get('interface_ids'):
            query = query.filter(self._model.interface_id.in_(search_params.get('interface_ids')))

        if search_params.get('interface_type'):
            query = query.filter(self._model.interface_type == search_params.get('interface_type'))

        if search_params.get('network'):
            query = query.filter(self._model.network == search_params.get('network'))

        if search_params.get('hardware_provider'):
            query = query.filter(self._model.hardware_provider == search_params.get('hardware_provider'))

        return [self._adaptor.to_domain_entity(db_model) for db_model in query.all()]

    def get_interface(self, interface_id, property_id=None):
        query_params = {'interface_id': interface_id}
        if property_id:
            query_params['property_id'] = property_id
        interface_model = self.get(self._model, **query_params)
        if interface_model:
            return self._adaptor.to_domain_entity(interface_model)
        raise ResourceNotFound(resource_name="Interface", resource_id=interface_id)

    def load_all(self, interface_ids) -> List[Interface]:
        query = self.session().query(self._model).filter(self._model.interface_id.in_(interface_ids))
        return [self._adaptor.to_domain_entity(db_model) for db_model in query.all()]

    def exists(self, interface_id, property_id):
        interface_model = self.get(self._model, interface_id=interface_id, property_id=property_id)
        return True if interface_model else False

    def delete_interface(self, interface_id, property_id=None):
        query_params = {'interface_id': interface_id}
        if property_id:
            query_params['property_id'] = property_id
        interface_model = self.get(self._model, **query_params)

        if interface_model is None:
            raise ResourceNotFound(resource_name="Interface id", resource_id=interface_id)

        self.delete(interface_model)

    def update(self, interface: Interface):
        interface_model = self._update(self._adaptor.to_db_entity(interface))
        return self._adaptor.to_domain_entity(interface_model)
