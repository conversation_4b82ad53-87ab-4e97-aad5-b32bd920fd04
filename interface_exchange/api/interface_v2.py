from flask import Blueprint

from object_registry import inject
from interface_exchange.api import ApiResponse
from interface_exchange.api.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from interface_exchange.api.schemas.request.interface_schemas import (
    InterfaceGetV2Schema,
    NewInterfaceV2Schema,
    UpdateInterfaceV2Schema,
)
from interface_exchange.api.schemas.response.interface_schema import InterfaceResponseV2Schema
from interface_exchange.application.services.interface_service import InterfaceService
from interface_exchange.config.swagger_config import swag_route


bp = Blueprint("InterfaceV2", __name__, url_prefix="/v2")


@swag_route
@bp.route("/properties/<property_id>/interfaces", methods=["GET"])
@schema_wrapper_parser(InterfaceGetV2Schema, param_type=RequestTypes.ARGS)
@inject(interface_service=InterfaceService)
def get_all_interfaces_v2(interface_service, parsed_request, property_id):
    """Get all interfaces details
    ---
    operationId: get_all_interfaces_v2
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - in: path
              name: property_id
              required: True
              type: string
            - in: query
              name: interface_search_params
              required: True
              schema: InterfaceGetV2Schema
        description: Get all interfaces details
        tags:
            - Interface Exchange / Interface
        responses:
            200:
                description: List of interfaces
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                interfaces:
                                    type: array
                                    items:
                                        $ref: "#/definitions/InterfaceResponseV2Schema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    interfaces = interface_service.get_all_interfaces(property_id, parsed_request)
    response = InterfaceResponseV2Schema(many=True).dump(interfaces)
    return ApiResponse.build(status_code=200, data=dict(interfaces=response.data))


@swag_route
@bp.route("/properties/<property_id>/interfaces", methods=["POST"])
@schema_wrapper_parser(NewInterfaceV2Schema)
@inject(interface_service=InterfaceService)
def create_interface(interface_service, parsed_request, property_id):
    """Create new interface
    ---
    operationId: create_interface_v2
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - in: path
              name: property_id
              required: True
              type: string
            - in: body
              name: interface_create_param
              required: True
              schema: NewInterfaceSchema
        description: Create new interface
        tags:
            - Interface Exchange / Interface
        responses:
            201:
                description: The schema of the interface created.
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            $ref: "#/definitions/InterfaceResponseV2Schema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
            409:
                description: The request could not be completed due to a conflict with the current state.
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    interface = interface_service.create_new_interface(
        property_id=property_id,
        interface_physical_id=parsed_request.get('interface_physical_id'),
        name=parsed_request.get('name'),
        interface_type=parsed_request.get('interface_type'),
        interface_sub_type=parsed_request.get('interface_sub_type'),
        hardware_provider=parsed_request.get('hardware_provider'),
        protocol=parsed_request.get('protocol'),
        network=parsed_request.get('network'),
        configs=parsed_request.get('configs'),
    )

    response = InterfaceResponseV2Schema().dump(interface)
    return ApiResponse.build(status_code=201, data=response.data)


@swag_route
@bp.route("/properties/<property_id>/interfaces/<interface_id>", methods=["PUT"])
@schema_wrapper_parser(UpdateInterfaceV2Schema)
@inject(interface_service=InterfaceService)
def update_interface(interface_service, parsed_request, property_id, interface_id):
    """Update interface
    ---
    operationId: update_interface_v2
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    put:
        parameters:
            - in: path
              name: property_id
              required: True
              type: string
            - in: path
              name: interface_id
              required: True
              type: string
            - in: body
              name: interface_update_param
              required: True
              schema: UpdateInterfaceSchema
        description: Update interface details
        tags:
            - Interface Exchange / Interface
        responses:
            200:
                description: Updated interface details
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                interfaces:
                                    type: array
                                    items:
                                        $ref: "#/definitions/InterfaceResponseV2Schema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    interface = interface_service.update_interface_v2(
        property_id=property_id,
        interface_id=interface_id,
        name=parsed_request.get('name'),
        protocol=parsed_request.get('protocol'),
        interface_physical_id=parsed_request.get('interface_physical_id'),
        network=parsed_request.get('network'),
        configs=parsed_request.get('configs'),
    )

    response = InterfaceResponseV2Schema().dump(interface)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route("/properties/<property_id>/interfaces/<interface_id>", methods=["DELETE"])
@inject(interface_service=InterfaceService)
def delete_interface(interface_service, property_id, interface_id):
    """Delete interface
    ---
    operationId: delete_interface_v2
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    delete:
        parameters:
            - in: path
              name: property_id
              required: True
              type: string
            - in: path
              name: interface_id
              required: True
              type: string
        description: Delete interface details
        tags:
            - Interface Exchange / Interface
        responses:
            204:
                description: The resource was deleted successfully.
    """
    interface_service.delete_interface(property_id, interface_id)
    return ApiResponse.build(status_code=204)
