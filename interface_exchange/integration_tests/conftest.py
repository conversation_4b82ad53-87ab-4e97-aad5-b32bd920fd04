import os

from interface_exchange.integration_tests.utilities import excel_utils
from interface_exchange.conftest import *


dir_path = os.path.dirname(os.path.abspath(__file__))
print("Module loaded: %s" % __name__)


@pytest.fixture(scope="session", autouse=True)
def excel_data():
    excel_utils.extract_excel_data(dir_path + '/resources/TestData.xlsx')


@pytest.fixture(scope="function", autouse=True)
def reporting():
    yield
    print("-------------TC ended ------------")


@pytest.fixture(scope="session", autouse=True)
def cleanup_global_variables():
    """Clean up global variables at the end of test session"""
    yield
    # Clear the created_interfaces global variable
    try:
        from interface_exchange.integration_tests.requests.interface_v2_request import created_interfaces
        created_interfaces.clear()
        print("Cleared global created_interfaces variable")
    except ImportError:
        pass  # Module might not be imported if no interface tests were run