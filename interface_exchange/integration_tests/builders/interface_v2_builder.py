import json
from interface_exchange.integration_tests.utilities.common_utils import sanitize_test_data
from interface_exchange.integration_tests.utilities import excel_utils


class InterfaceV2:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.property_id = sanitize_test_data(test_data['property_id'])
        self.name = sanitize_test_data(test_data['name'])
        self.interface_physical_id = sanitize_test_data(test_data['interface_physical_id'])
        self.interface_type = sanitize_test_data(test_data['interface_type'])
        self.interface_sub_type = sanitize_test_data(test_data['interface_sub_type'])
        self.hardware_provider = sanitize_test_data(test_data['hardware_provider'])
        self.protocol = sanitize_test_data(test_data['protocol'])
        self.network = sanitize_test_data(test_data['network'])

        # Parse configs from test data if available
        if sanitize_test_data(test_data['configs']):
            self.configs = json.loads(test_data['configs'])
        else:
            self.configs = {}


class DeleteInterfaceV2:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.property_id = sanitize_test_data(test_data['property_id'])
