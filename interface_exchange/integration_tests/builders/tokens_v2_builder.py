from interface_exchange.integration_tests.utilities.common_utils import sanitize_test_data
from interface_exchange.integration_tests.utilities import excel_utils


class GenerateAccessToken:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.property_id = sanitize_test_data(test_data['property_id'])

        # For empty request body test case
        if test_case_id == "TokensV2_Generate_04":
            self.empty_body = True


class OverrideAccessToken:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.property_id = sanitize_test_data(test_data['property_id'])
