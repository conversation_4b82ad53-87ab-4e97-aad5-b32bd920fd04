import json
from interface_exchange.integration_tests.utilities.common_utils import sanitize_test_data
from interface_exchange.integration_tests.utilities import excel_utils


class GetBookingEvents:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.property_id = sanitize_test_data(test_data['property_id'])
        self.room_number = sanitize_test_data(test_data['room_number'])


class ResendBookingEvents:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.property_id = sanitize_test_data(test_data['property_id'])
        
        # Parse booking events from test data
        if sanitize_test_data(test_data['booking_events']):
            self.booking_events = json.loads(test_data['booking_events'])
        else:
            self.booking_events = []
