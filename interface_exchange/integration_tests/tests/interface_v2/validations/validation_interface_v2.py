import json

from interface_exchange.integration_tests.config import sheet_names
from interface_exchange.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from interface_exchange.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationInterfaceV2:
    def __init__(self, client_, test_case_id, response, interface_v2_request):
        self.test_data = get_test_case_data(sheet_names.interface_v2_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.interface_v2_request = interface_v2_request

    def validate_create_interface_response(self):
        created_interface_response = self.response['data']
        created_interface_id = created_interface_response['interface_id']
        self.assert_response_data(self.test_data, created_interface_response)

        get_interface_response = self.interface_v2_request.get_interfaces_v2_request(self.client,
                                                                                     self.test_data['property_id'], 200)
        interface_found = False
        for interface in get_interface_response['data']['interfaces']:
            if interface['interface_id'] == created_interface_id:
                interface_found = True
                self.assert_response_data(self.test_data, interface)
                break
        assert_(interface_found, True,
                f"Created interface with ID {created_interface_id} not found in get_interfaces response")

        get_interface_using_hardware_provider_response = self.interface_v2_request.get_interfaces_v2_request(
            self.client, self.test_data['property_id'],
            200, 'hardware_provider={0}'.format(self.test_data['hardware_provider']))
        interface_found = False
        for interface in get_interface_using_hardware_provider_response['data']['interfaces']:
            if interface['interface_id'] == created_interface_id:
                interface_found = True
                self.assert_response_data(self.test_data, interface)
                break
        assert_(interface_found, True,
                f"Created interface with ID {created_interface_id} not found in get_interfaces response")

    def validate_update_interface_response(self, interface_id):
        response_data = self.response['data']

        self.assert_response_data(self.test_data, response_data)

        get_interface_response = self.interface_v2_request.get_interfaces_v2_request(self.client,
                                                                                     self.test_data['property_id'], 200)

        # Find the created interface in the response
        interface_found = False
        for interface in get_interface_response['data']['interfaces']:
            if interface['interface_id'] == int(interface_id):
                interface_found = True
                self.assert_response_data(self.test_data, interface)
                break

        assert_(interface_found, True)

    def assert_response_data(self, expected, actual):
        if sanitize_test_data(expected['property_id']):
            assert_(actual['property_id'], expected['property_id'])
        if sanitize_test_data(expected['name']):
            assert_(actual['name'], expected['name'])
        if sanitize_test_data(expected['protocol']):
            assert_(actual['protocol'], expected['protocol'])
        if sanitize_test_data(expected['hardware_provider']):
            assert_(actual['hardware_provider'], expected['hardware_provider'])
        if sanitize_test_data(expected['interface_type']):
            assert_(actual['interface_type'], expected['interface_type'])
        if sanitize_test_data(expected['network']):
            assert_(actual['network'], expected['network'])
        if sanitize_test_data(expected['interface_sub_type']):
            assert_(actual['interface_sub_type'], expected['interface_sub_type'])
        else:
            assert actual['interface_sub_type'] is None
        if sanitize_test_data(expected['interface_physical_id']):
            assert_(actual['interface_physical_id'], expected['interface_physical_id'])
        if sanitize_test_data(expected['configs']):
            expected_configs = json.loads(expected['configs'])
            actual_configs = actual['configs']
            for key, value in actual_configs.items():
                if value is not None:
                    assert_(value, expected_configs.get(key),
                            f"Config value for key '{key}' should match")
        else:
            assert actual['configs'] == {} or actual['configs'] is None
