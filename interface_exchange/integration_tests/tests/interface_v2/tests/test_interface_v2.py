import pytest

from interface_exchange.integration_tests.tests.base_test import BaseTest
from interface_exchange.integration_tests.tests.interface_v2.validations.validation_interface_v2 import \
    ValidationInterfaceV2
from interface_exchange.integration_tests.config.common_config import *
from interface_exchange.integration_tests.conftest import *


class TestInterfaceV2(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("InterfaceV2_Create_01", "Create interface with valid data having configs dict as well",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_02", "Create interface with invalid property ID and having all the data",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_03", "Create interface without having config key",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_04", "Create interface having empty config key",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_05", "Create interface having null config key",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_06", "Create interface with config key data",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_07", "Create interface without having hardware key",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_08", "Create interface having hardware key as null",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_09", "Create interface having hardware key as ' LEVOTELS '",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_10", "Create interface having interface_sub_type as null",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_11", "Create interface having interface_sub_type as 'testing'",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_12", "Create interface without having interface_type key",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_13", "Create interface having interface_type key as null",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_14", "Create interface having interface_type key as 'WIFI'",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_15", "Create interface without having name key",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_16", "Create interface having name key as null",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_17", "Create interface having name key as Testing's Cloud Kitchen 12",
             None, 201, "", "", "", "", False, ""),
            ("InterfaceV2_Create_18", "Hit the api for name which is already  exists",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_19", "Create interface without having protocol key",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_20", "Create interface having protocol key as null",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_21", "Create interface having protocol key as 'test'",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_22", "Create interface without having network key",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_23", "Create interface having network key as null",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_24", "Create interface having network key as 'lan'",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_25", "Create interface without having interface_physical_id key",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_26", "Create interface having interface_physical_id key as null",
             None, 400, "", "", "", "", False, ""),
            ("InterfaceV2_Create_27", "Create interface having interface_physical_id key as 'testing'",
             None, 201, "", "", "", "", False, ""),

        ])
    @pytest.mark.regression
    def test_create_interface_v2(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                 error_code, error_message, dev_message, error_payload, skip_case,
                                 skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if previous_actions:
            self.common_request_caller(client_, previous_actions)
        response = self.interface_v2_request.create_interface_v2_request(client_, test_case_id, status_code)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_create_interface(client_, test_case_id, response, self.interface_v2_request)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("InterfaceV2_Update_01", "Hit the api with interface id which wont exist",
             None, 404, "", "Interface not found: ", "", "", False, ""),
            ("InterfaceV2_Update_02", "Hit the api with interfaceid which exists but hotel id is different",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 404, "", "Interface not found: ", "", "",
             False, ""),
            ("InterfaceV2_Update_03", "Hit the api with correctly mapped hotelid and interfaceid",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 200, "", "", "", "", False, ""),
            ("InterfaceV2_Update_04", "Hit the api and add configs. In creation configs was null",
             [{"id": "InterfaceV2_Create_05", "type": "create_interface_v2"}], 200, "", "", "", "", False, ""),
            ("InterfaceV2_Update_05", "Hit the api and set configs to null. In creation configs was present",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 200, "", "", "", "", False, ""),
            ("InterfaceV2_Update_06", "Hit the api and don't pass name key",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 400, "", "Request Invalid", "", "",
             False, ""),
            ("InterfaceV2_Update_07",
             "Hit the api and change the name, hardware provider, protocol,network,interface_type",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 200, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_update_interface_v2(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                 error_code, error_message, dev_message, error_payload, skip_case,
                                 skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if previous_actions:
            interface_id = self.interface_v2_request.get_created_interface_id(previous_actions[0]['id'])
            if interface_id is None:
                self.common_request_caller(client_, previous_actions)
                interface_id = self.interface_v2_request.get_created_interface_id(previous_actions[0]['id'])
            interface_id = str(interface_id)
        else:
            interface_id = '897'
        response = self.interface_v2_request.update_interface_v2_request(client_, test_case_id, status_code,
                                                                         interface_id)
        if status_code in ERROR_CODES:
            if test_case_id in ('InterfaceV2_Update_01', 'InterfaceV2_Update_02'):
                error_message = error_message + interface_id
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_update_interface(client_, test_case_id, response, self.interface_v2_request, interface_id)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("InterfaceV2_Delete_01", "Delete interface with valid data",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 204, "", "", "", "", False, ""),
            ("InterfaceV2_Delete_02", "Delete interface with invalid property ID",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 404, "", "Interface id not found: ", "",
             "", False, ""),
            ("InterfaceV2_Delete_03", "Delete interface with invalid interface ID",
             [{"id": "InterfaceV2_Create_01", "type": "create_interface_v2"}], 404, "", "Interface id not found: ", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_delete_interface_v2(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                 error_code, error_message, dev_message, error_payload, skip_case,
                                 skip_message):
        if skip_case:
            pytest.skip(skip_message)
        interface_id = self.interface_v2_request.get_created_interface_id(previous_actions[0]['id'])
        if interface_id is None:
            self.common_request_caller(client_, previous_actions)
            interface_id = self.interface_v2_request.get_created_interface_id(previous_actions[0]['id'])
        interface_id = str(interface_id)
        if test_case_id=='InterfaceV2_Delete_03':
            interface_id = "122"
        response = self.interface_v2_request.delete_interface_v2_request(client_, test_case_id, status_code,
                                                                         interface_id)
        if status_code in ERROR_CODES:
            if test_case_id in ('InterfaceV2_Delete_02', 'InterfaceV2_Delete_03'):
                error_message = error_message + interface_id
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            pass
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation_create_interface(client_, test_case_id, response, interface_v2_request):
        validation = ValidationInterfaceV2(client_, test_case_id, response, interface_v2_request)
        validation.validate_create_interface_response()

    @staticmethod
    def validation_update_interface(client_, test_case_id, response, interface_v2_request, interface_id):
        validation = ValidationInterfaceV2(client_, test_case_id, response, interface_v2_request)
        validation.validate_update_interface_response(interface_id)
