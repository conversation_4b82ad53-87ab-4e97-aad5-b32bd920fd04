from interface_exchange.integration_tests.config import sheet_names
from interface_exchange.integration_tests.utilities.common_utils import assert_
from interface_exchange.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationBookingEvents:
    def __init__(self, client_, test_case_id, response, booking_events_request):
        self.test_data = get_test_case_data(sheet_names.booking_events_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.booking_events_request = booking_events_request

    def validate_get_booking_events_response(self):
        # Validate that the response contains booking events
        assert_(isinstance(self.response['data'], list), True)
        
        # If there are booking events, validate their structure
        if self.response['data']:
            for booking_event in self.response['data']:
                assert_('property_id' in booking_event, True)
                assert_('booking_id' in booking_event, True)
                assert_('room_stay_id' in booking_event, True)
                assert_('guest_stay_id' in booking_event, True)
                assert_('room_id' in booking_event, True)
                assert_('room_number' in booking_event, True)
                assert_('checkin_at' in booking_event, True)
                assert_('checkout_at' in booking_event, True)
                assert_('first_name' in booking_event, True)
                assert_('last_name' in booking_event, True)
                assert_('status' in booking_event, True)
                
                # Validate property_id if specified in test data
                if self.test_data['property_id'] != 'NULL':
                    assert_(booking_event['property_id'], self.test_data['property_id'])
                
                # Validate room_number if specified in test data
                if self.test_data['room_number']:
                    assert_(booking_event['room_number'], self.test_data['room_number'])

    def validate_resend_booking_events_response(self):
        # Validate that the response contains booking events
        assert_(isinstance(self.response['data'], list), True)
        
        # If there are booking events, validate their structure
        if self.response['data']:
            for booking_event in self.response['data']:
                assert_('property_id' in booking_event, True)
                assert_('booking_id' in booking_event, True)
                assert_('room_stay_id' in booking_event, True)
                assert_('guest_stay_id' in booking_event, True)
                assert_('room_id' in booking_event, True)
                assert_('room_number' in booking_event, True)
                assert_('checkin_at' in booking_event, True)
                assert_('checkout_at' in booking_event, True)
                assert_('first_name' in booking_event, True)
                assert_('last_name' in booking_event, True)
                assert_('status' in booking_event, True)
                
                # Validate property_id if specified in test data
                if self.test_data['property_id'] != 'NULL':
                    assert_(booking_event['property_id'], self.test_data['property_id'])
