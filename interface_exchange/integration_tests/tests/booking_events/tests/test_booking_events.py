import pytest

from interface_exchange.integration_tests.tests.base_test import BaseTest
from interface_exchange.integration_tests.tests.booking_events.validations.validation_booking_events import ValidationBookingEvents
from interface_exchange.integration_tests.config.common_config import *
from interface_exchange.integration_tests.conftest import *


class TestBookingEvents(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("BookingEvents_Get_01", "Get booking events for a valid property and room number",
             None, 200, "", "", "", "", False, ""),
            ("BookingEvents_Get_02", "Get booking events with invalid property ID",
             None, 400, "", "", "", "", False, ""),
            ("BookingEvents_Get_03", "Get booking events with missing room number",
             None, 400, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_get_booking_events(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                error_code, error_message, dev_message, error_payload, skip_case,
                                skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if previous_actions:
            self.common_request_caller(client_, previous_actions)
        response = self.booking_events_request.get_booking_events_request(client_, test_case_id, status_code)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_get_booking_events(client_, test_case_id, response, self.booking_events_request)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("BookingEvents_Resend_01", "Resend booking events for a valid property",
             None, 200, "", "", "", "", False, ""),
            ("BookingEvents_Resend_02", "Resend booking events with invalid property ID",
             None, 400, "", "", "", "", False, ""),
            ("BookingEvents_Resend_03", "Resend booking events with invalid booking events data",
             None, 400, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_resend_booking_events(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                   error_code, error_message, dev_message, error_payload, skip_case,
                                   skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if previous_actions:
            self.common_request_caller(client_, previous_actions)
        response = self.booking_events_request.resend_booking_events_request(client_, test_case_id, status_code)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_resend_booking_events(client_, test_case_id, response, self.booking_events_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation_get_booking_events(client_, test_case_id, response, booking_events_request):
        validation = ValidationBookingEvents(client_, test_case_id, response, booking_events_request)
        validation.validate_get_booking_events_response()

    @staticmethod
    def validation_resend_booking_events(client_, test_case_id, response, booking_events_request):
        validation = ValidationBookingEvents(client_, test_case_id, response, booking_events_request)
        validation.validate_resend_booking_events_response()
