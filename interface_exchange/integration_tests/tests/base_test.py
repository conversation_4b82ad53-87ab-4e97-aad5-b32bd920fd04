from interface_exchange.integration_tests.utilities.common_utils import assert_
from interface_exchange.integration_tests.requests.partner_request import *
from interface_exchange.integration_tests.requests.minibar_items_request import *
from interface_exchange.integration_tests.requests.booking_charges_request import *
from interface_exchange.integration_tests.requests.key_issuance_request import *
from interface_exchange.integration_tests.requests.dnd_request import *
from interface_exchange.integration_tests.config.common_config import *
from interface_exchange.integration_tests.requests.interface_request import *
from interface_exchange.integration_tests.requests.wakeup_call_request import *
from interface_exchange.integration_tests.requests.booking_events_request import *
from interface_exchange.integration_tests.requests.interface_v2_request import *
from interface_exchange.integration_tests.requests.interface_configs_request import *
from interface_exchange.integration_tests.requests.tokens_v2_request import *

class BaseTest(object):
    partner_apis_request = PartnerRequests()
    minibar_items_request = MinibarItemsRequests()
    booking_charges_request = MinibarChargesRequests()
    key_issuance_request = KeyIssuanceRequests()
    dnd_request = DNDRequests()
    interface_request = InterfaceRequest()
    wakeup_call_request = WakeUpCallRequest()
    booking_events_request = BookingEventsRequests()
    interface_v2_request = InterfaceV2Requests()
    interface_configs_request = InterfaceConfigsRequests()
    tokens_v2_request = TokensV2Requests()

 #   booking_request = BookingRequests()

    def common_request_caller(self, client, test_case_id_plus_action_to_be_performed_list):
        results = []
        for action in test_case_id_plus_action_to_be_performed_list:
            # Handle dictionary format with id and type
            if isinstance(action, dict) and 'id' in action and 'type' in action:
                test_case_id = action['id']
                action_type = action['type']
                user_type = action['user_type'] if 'user_type' in action else None

                if action_type == 'create_minibar_item':
                    self.minibar_items_request.create_edit_minibar_items_request(client, test_case_id, 200, PROPERTY_ID)
                elif action_type == 'create_booking':
                    self.booking_request.create_booking_request(test_case_id, 200)
                elif action_type == 'check_in_booking':
                    self.booking_request.check_in_booking_request(test_case_id, '6914-8305-7395', 200)
                elif action_type == 'issue_key':
                    self.key_issuance_request.create_key_issuance_key_request(client, test_case_id, 200)
                elif action_type == 'patch_an_issued_key':
                    self.key_issuance_request.update_key_issuance_key_request(client, test_case_id, 200)
                elif action_type == 'create_wakeup_call':
                    self.wakeup_call_request.create_wakeup_call_request(client, test_case_id, 201)
                elif action_type == 'generate_access_token':
                    result = self.tokens_v2_request.generate_access_token_request(client, test_case_id, 200)
                    results.append(result)
                elif action_type == 'create_interface_v2':
                    self.interface_v2_request.create_interface_v2_request(client, test_case_id, 201)
                else:
                    raise ValueError(f"Action type '{action_type}' is not handled in Common request caller")
            else:
                raise ValueError(f"Invalid action format: {action}")

        return results

    @staticmethod
    def response_validation_negative_cases(response, code, message, dev_message, extra_payload):
        if code:
            assert_(response['errors'][0]['code'], code)
        if message:
            assert_(response['errors'][0]['message'], message)
        if dev_message:
            assert_(response['errors'][0]['developer_message'], dev_message)
        if extra_payload:
            extra_payload_json = json.loads(extra_payload)
            assert_(response['errors'][0]['extra_payload'], extra_payload_json)
