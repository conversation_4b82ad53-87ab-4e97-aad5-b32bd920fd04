from interface_exchange.integration_tests.tests.base_test import BaseTest
from interface_exchange.integration_tests.tests.tokens_v2.validations.validation_tokens_v2 import ValidationTokensV2
from interface_exchange.integration_tests.config.common_config import *
from interface_exchange.integration_tests.conftest import *


class TestTokensV2(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("TokensV2_Generate_01", "Generate access token for a valid property",
             None, 200, "", "", "", "", False, ""),
            ("TokensV2_Generate_02", "Generate access token with invalid property ID",
             None, 200, "", "", "", "", False, ""),
            ("TokensV2_Generate_03", "Generate access token without property ID",
             None, 404, "", "", "", "", False, ""),
            ("TokensV2_Generate_04", "Generate access token with empty request body",
             None, 200, "", "", "", "", False, ""),
            ("TokensV2_Generate_05", "Generate access token for inactive property",
             None, 200, "", "", "", "", False, ""),
            ("TokensV2_Generate_06", "Generate access token for property that already has a token",
             None, 200, "", "", "", "", False, "")
        ])
    @pytest.mark.regression
    def test_generate_access_token(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                   error_code, error_message, dev_message, error_payload, skip_case,
                                   skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        # Normal flow for all test cases
        response = self.tokens_v2_request.generate_access_token_request(client_, test_case_id, status_code)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_generate_access_token(client_, test_case_id, response, self.tokens_v2_request)
        else:
            assert False, "Response status code is not matching"

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("TokensV2_Override_01", "Override access token for a valid property",
             [{"id": "TokensV2_Generate_01", "type": "generate_access_token"}], 200, "", "", "", "", False, ""),
            ("TokensV2_Override_02", "Override access token for property where no token exists",
             None, 200, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_override_access_token(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                   error_code, error_message, dev_message, error_payload, skip_case,
                                   skip_message):
        if skip_case:
            pytest.skip(skip_message)

        # Execute previous actions if any
        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        # Override the access token
        override_response = self.tokens_v2_request.override_access_token_request(client_, test_case_id, status_code)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(override_response, error_code, error_message, dev_message,
                                                    error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_override_access_token(client_, test_case_id, override_response, self.tokens_v2_request)
        else:
            assert False, "Response status code is not matching"


    @staticmethod
    def validation_generate_access_token(client_, test_case_id, response, tokens_v2_request):
        validation = ValidationTokensV2(client_, test_case_id, response, tokens_v2_request)
        validation.validate_generate_access_token_response()

    @staticmethod
    def validation_override_access_token(client_, test_case_id, response, tokens_v2_request):
        validation = ValidationTokensV2(client_, test_case_id, response, tokens_v2_request)
        validation.validate_override_access_token_response()