from interface_exchange.integration_tests.config import sheet_names
from interface_exchange.integration_tests.utilities.common_utils import assert_
from interface_exchange.integration_tests.utilities.excel_utils import get_test_case_data


# Token map to store tokens by property_id and test case ID
token_map = {}

# Store the key from TokensV2_Generate_01 for comparison with TokensV2_Generate_06
generate_01_key = None

class ValidationTokensV2:
    def __init__(self, client_, test_case_id, response, tokens_v2_request):
        self.test_data = get_test_case_data(sheet_names.tokens_v2_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.tokens_v2_request = tokens_v2_request
        self.test_case_id = test_case_id

    def validate_generate_access_token_response(self):
        global generate_01_key

        # Get the property_id and key from the generate response
        property_id = self.response['data']['property_id']
        access_token = self.response['data']['key']

        # Store the key from TokensV2_Generate_01 for comparison with TokensV2_Generate_06
        if self.test_case_id == "TokensV2_Generate_01":
            generate_01_key = access_token

        # For TokensV2_Generate_06, check that the key is the same as the one from TokensV2_Generate_01
        if self.test_case_id == "TokensV2_Generate_06":
            assert_(access_token, generate_01_key, "The key from TokensV2_Generate_06 should match the key from TokensV2_Generate_01")
            print(f"Verified key from TokensV2_Generate_06 matches key from TokensV2_Generate_01 for property {property_id}")
            return

        # Call the get_access_token API with the same property_id
        get_access_token_api_response = self.tokens_v2_request.get_access_token_request(self.client, property_id, 200)

        # Get the key from the get response
        access_token_from_get_api = get_access_token_api_response['data']['key']

        # Verify that the keys match
        assert_(access_token_from_get_api, access_token, "The key from GET should match the key from POST Access Token")

    def validate_override_access_token_response(self):
        # Get the property_id and key from the override response
        property_id = self.response['data']['property_id']
        override_key = self.response['data']['key']

        # Call the get_access_token API with the same property_id
        get_response = self.tokens_v2_request.get_access_token_request(self.client, property_id, 200)

        # Get the key from the get response
        get_key = get_response['data']['key']

        # Verify that the keys match
        assert_(get_key, override_key, "The key from GET should match the key from OVERRIDE")

