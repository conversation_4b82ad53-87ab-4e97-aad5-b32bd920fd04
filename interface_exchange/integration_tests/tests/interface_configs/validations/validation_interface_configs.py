from interface_exchange.integration_tests.utilities.common_utils import assert_


class ValidationInterfaceConfigs:
    def __init__(self, response):
        self.response = response

    def validate_get_interface_configs_response(self):
        # Validate that the response contains interface configs
        assert_(isinstance(self.response['data'], list), True)
        
        # If there are interface configs, validate their structure
        if self.response['data']:
            for interface_config in self.response['data']:
                assert_('interface_type' in interface_config, True)
                assert_('hardware_provider' in interface_config, True)
                assert_('protocol' in interface_config, True)
                assert_('network' in interface_config, True)
                assert_('interface_physical_id' in interface_config, True)
