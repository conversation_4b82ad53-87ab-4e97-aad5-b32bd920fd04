import pytest

from interface_exchange.integration_tests.tests.base_test import BaseTest
from interface_exchange.integration_tests.tests.interface_configs.validations.validation_interface_configs import ValidationInterfaceConfigs
from interface_exchange.integration_tests.config.common_config import *
from interface_exchange.integration_tests.conftest import *


class TestInterfaceConfigs(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, error_code, error_message, "
        "dev_message, error_payload, skip_case, skip_message", [
            ("InterfaceConfigs_Get_01", "Get interface configs",
             None, 200, "", "", "", "", False, ""),
        ])
    @pytest.mark.regression
    def test_get_interface_configs(self, client_, test_case_id, tc_description, previous_actions, status_code,
                                  error_code, error_message, dev_message, error_payload, skip_case,
                                  skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if previous_actions:
            self.common_request_caller(client_, previous_actions)
        response = self.interface_configs_request.get_interface_configs_request(client_, status_code)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation_get_interface_configs(response)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation_get_interface_configs(response):
        validation = ValidationInterfaceConfigs(response)
        validation.validate_get_interface_configs_response()
