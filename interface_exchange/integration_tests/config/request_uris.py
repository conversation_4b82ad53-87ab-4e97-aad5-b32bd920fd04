##################### PartnerAPIs ######################

# Get Guest Details
get_guest_details_uri = 'public/v1/rooms/{0}/guests'

# Post WiFi Charges
add_wifi_charges_uri = 'public/v1/rooms/{0}/charges'

# Get Minibar Item Details
get_minibar_items_details_uri = 'interface-exchange/v1/minibar-items?property_id={0}'

# Create and Edit Minibar Details
create_edit_minibar_items_uri = 'interface-exchange/v1/minibar-items'

# Remove Minibar Details
remove_minibar_items_uri = 'interface-exchange/v1/minibar-items/{0}'

# Add minibar charges
add_minibar_charges_uri = 'public/v1/minibar-charges'

##################### booking ######################

# Create Booking
GET_BOOKING_URI = '/v1/bookings/{0}'

# Get Expenses
GET_EXPENSE_DETAIL = '/v1/bookings/{0}/expenses'

# Get all rooms with booking
GET_ALL_ROOMS_WITH_BOOKING_URI = '/v1/hotels/{0}/rooms?current_status={1}'

##################### Key_Issuance ######################

# Create Key Issuance
create_key_issuance_uri = '/interface-exchange/v1/key-issuance'

update_key_issuance_uri = '/interface-exchange/v1/key-issuance/{0}'

get_key_issuance_uri = '/interface-exchange/v1/key-issuance?property_id={0}&booking_id={1}'

####################### DND Status #########################

update_dnd_status_uri = "/interface-exchange/v1/dnd"

get_dnd_status_uri = "/interface-exchange/v1/dnd?property_id={0}&booking_id={1}&room_id={2}"

####################### Interface #########################

get_interface_with_interface_type = '/interface-exchange/v1/interfaces?property_id={0}&interface_type={1}'

get_interface_without_property_id = '/interface-exchange/v1/interfaces'

get_interface = '/interface-exchange/v1/interfaces?property_id={0}'

####################### Interface V2 #########################

get_interfaces_v2_uri = '/interface-exchange/v2/properties/{0}/interfaces'
create_interface_v2_uri = '/interface-exchange/v2/properties/{0}/interfaces'
update_interface_v2_uri = '/interface-exchange/v2/properties/{0}/interfaces/{1}'
delete_interface_v2_uri = '/interface-exchange/v2/properties/{0}/interfaces/{1}'

####################### Interface Configs #########################

get_interface_configs_uri = '/interface-exchange/v1/interface-configs'

####################### Booking Events #########################

get_booking_events_uri = '/interface-exchange/v1/properties/{0}/booking-events'
resend_booking_events_uri = '/interface-exchange/v1/properties/{0}/resend-booking-events'

####################### Tokens V2 #########################

generate_access_token_uri = '/interface-exchange/v2/properties/{0}/access-token'
override_access_token_uri = '/interface-exchange/v2/properties/{0}/override-access-token'
get_access_token_uri = '/interface-exchange/v2/properties/{0}/access-token'

####################### Wake up call #########################

wake_up_call_uri = '/interface-exchange/v1/wakeup-calls'
get_wake_up_call_uri = '/interface-exchange/v1/wakeup-calls?property_id={0}&booking_id={1}&room_id={2}'
update_wakeup_call_uri = '/interface-exchange/v1/wakeup-calls'
delete_wake_up_call_uri = '/interface-exchange/v1/wakeup-calls/{wakeup_call_id}'
