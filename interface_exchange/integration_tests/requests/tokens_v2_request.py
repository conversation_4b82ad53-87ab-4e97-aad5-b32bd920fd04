from interface_exchange.integration_tests.requests.base_request import BaseRequest
from interface_exchange.integration_tests.config.request_uris import *
from interface_exchange.integration_tests.config.sheet_names import *
from interface_exchange.integration_tests.utilities.common_utils import del_none
from interface_exchange.integration_tests.builders import tokens_v2_builder
from interface_exchange.integration_tests.config.common_config import SUCCESS_CODES


class TokensV2Requests(BaseRequest):

    def generate_access_token_request(self, client, test_case_id, status_code):
        test_data = tokens_v2_builder.GenerateAccessToken(sheet_name=tokens_v2_sheet_name, test_case_id=test_case_id)

        # Handle special case for no property ID (404 error)
        if test_case_id == "TokensV2_Generate_03":
            uri = generate_access_token_uri.format('').rstrip('/')
        elif test_data.property_id == 'NULL':
            uri = generate_access_token_uri.format('NULL')
        else:
            uri = generate_access_token_uri.format(test_data.property_id)

        # Use empty request body for the empty body test case
        request_body = "{}"

        response = self.request_processor(client, 'POST', uri, status_code, test_data.property_id, request_body)

        if status_code in SUCCESS_CODES:
            self.property_id = test_data.property_id
            self.access_token = response['data']['key']

        return response

    def override_access_token_request(self, client, test_case_id, status_code):
        test_data = tokens_v2_builder.OverrideAccessToken(sheet_name=tokens_v2_sheet_name, test_case_id=test_case_id)

        if test_data.property_id == 'NULL':
            uri = override_access_token_uri.format('NULL')
        else:
            uri = override_access_token_uri.format(test_data.property_id)

        # Use empty request body
        request_body = "{}"

        response = self.request_processor(client, 'POST', uri, status_code, test_data.property_id, request_body)

        if status_code in SUCCESS_CODES:
            self.property_id = test_data.property_id
            if 'key' in response['data']:
                self.access_token = response['data']['key']

        return response

    def get_access_token_request(self, client, property_id, status_code, tenant_id=None):
        headers = {'X-Tenant-ID': tenant_id} if tenant_id else {}
        uri = get_access_token_uri.format(property_id)
        response = self.request_processor(client, 'GET', uri, status_code, property_id, None, headers)
        return response
