import json
from interface_exchange.integration_tests.requests.base_request import BaseRequest
from interface_exchange.integration_tests.config.request_uris import *
from interface_exchange.integration_tests.config.sheet_names import *
from interface_exchange.integration_tests.utilities.common_utils import del_none
from interface_exchange.integration_tests.builders import interface_v2_builder
from interface_exchange.integration_tests.config.common_config import SUCCESS_CODES

# Global variable to store test_case_id to interface_id mapping
created_interfaces = {}


class InterfaceV2Requests(BaseRequest):

    def get_created_interface_id(self, test_case_id):
        interface_id = created_interfaces.get(test_case_id)
        return interface_id

    def get_interfaces_v2_request(self, client, property_id, status_code, query_param=None):
        uri = get_interfaces_v2_uri.format(property_id)
        if query_param:
            uri += "?" + "&".join(query_param)
        response = self.request_processor(client, 'GET', uri, status_code, property_id)
        return response

    def create_interface_v2_request(self, client, test_case_id, status_code):
        test_data = interface_v2_builder.InterfaceV2(sheet_name=interface_v2_sheet_name,
                                                           test_case_id=test_case_id)
        if test_case_id not in ('InterfaceV2_Create_01', 'InterfaceV2_Create_05', 'InterfaceV2_Create_08',
                                'InterfaceV2_Create_10', 'InterfaceV2_Create_13', 'InterfaceV2_Create_16'):
            request_json = json.dumps(del_none(test_data.__dict__))
        else:
            request_json = json.dumps(test_data.__dict__)

        if test_data.property_id == 'NULL':
            uri = create_interface_v2_uri.format('NULL')
        else:
            uri = create_interface_v2_uri.format(test_data.property_id)

        response = self.request_processor(client, 'POST', uri, status_code, test_data.property_id, request_json)

        if status_code in SUCCESS_CODES:
            self.property_id = test_data.property_id
            self.interface_id = response['data']['interface_id']
            # Store the interface_id in global variable for reuse in other tests
            created_interfaces[test_case_id] = response['data']['interface_id']
        return response

    def update_interface_v2_request(self, client, test_case_id, status_code, interface_id=None):
        test_data = interface_v2_builder.InterfaceV2(sheet_name=interface_v2_sheet_name,
                                                           test_case_id=test_case_id)
        request_json = json.dumps(del_none(test_data.__dict__))
        if interface_id is None:
            interface_id = getattr(self, 'interface_id', None)
        uri = delete_interface_v2_uri.format(test_data.property_id, interface_id)
        response = self.request_processor(client, 'PUT', uri, status_code, test_data.property_id, request_json)

        if status_code in SUCCESS_CODES:
            self.property_id = test_data.property_id

        return response

    def delete_interface_v2_request(self, client, test_case_id, status_code, interface_id=None):
        test_data = interface_v2_builder.DeleteInterfaceV2(sheet_name=interface_v2_sheet_name,
                                                           test_case_id=test_case_id)

        uri = delete_interface_v2_uri.format(test_data.property_id, interface_id or self.interface_id)

        response = self.request_processor(client, 'DELETE', uri, status_code, test_data.property_id)

        if status_code in SUCCESS_CODES:
            self.property_id = test_data.property_id

        return response
