import json
from interface_exchange.integration_tests.requests.base_request import BaseRequest
from interface_exchange.integration_tests.config.request_uris import *
from interface_exchange.integration_tests.config.sheet_names import *
from interface_exchange.integration_tests.utilities.common_utils import del_none
from interface_exchange.integration_tests.builders import booking_events_builder
from interface_exchange.integration_tests.config.common_config import SUCCESS_CODES
from interface_exchange.integration_tests.mocker import mock_catalog_client
from interface_exchange.integration_tests.mocker import mock_push_client
from interface_exchange.integration_tests.mocker import mock_crs_client


class BookingEventsRequests(BaseRequest):

    def get_booking_events_request(self, client, test_case_id, status_code):
        test_data = booking_events_builder.GetBookingEvents(sheet_name=booking_events_sheet_name, test_case_id=test_case_id)
        
        property_id_value = 'NULL' if test_data.property_id == 'NULL' else test_data.property_id
        uri = get_booking_events_uri.format(property_id_value)
            
        if test_data.room_number:
            uri += f"?room_number={test_data.room_number}"
            
        with mock_catalog_client(), mock_crs_client():
            response = self.request_processor(client, 'GET', uri, status_code, test_data.property_id)
            
        if status_code in SUCCESS_CODES:
            self.property_id = test_data.property_id
            
        return response

    def resend_booking_events_request(self, client, test_case_id, status_code):
        test_data = booking_events_builder.ResendBookingEvents(sheet_name=booking_events_sheet_name, test_case_id=test_case_id)
        request_json = json.dumps(del_none(test_data.booking_events))
        
        if test_data.property_id == 'NULL':
            uri = resend_booking_events_uri.format('NULL')
        else:
            uri = resend_booking_events_uri.format(test_data.property_id)
            
        with mock_catalog_client(), mock_crs_client(), mock_push_client():
            response = self.request_processor(client, 'POST', uri, status_code, test_data.property_id, request_json)
            
        if status_code in SUCCESS_CODES:
            self.property_id = test_data.property_id
            
        return response
