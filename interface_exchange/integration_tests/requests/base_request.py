import logging

from flask import json

from interface_exchange.integration_tests.config import common_config
from interface_exchange.integration_tests.utilities.common_utils import assert_

logger = logging.getLogger(__name__)


class BaseRequest(object):
    def __init__(self):
        self.booking_id = None
        self.property_id = None
        self.minibar_item_id = []
        self.bill_id = None
        self.crs_booking_id = None
        self.action_id = None
        self.key_issue_id = None
        self.room_id = None
        self.wakeup_call_id = None

    def request_processor(self, client_, request_type, url, status_code, hotel_id=None, request_json=None,
                          custom_headers=None, user_type=None, parameters=None, user=None):
        headers = {'Content-Type': 'application/json'}

        # Add custom headers if provided
        if custom_headers:
            headers.update(custom_headers)
        client_type = {
            "POST": client_.post,
            "PATCH": client_.patch,
            "PUT": client_.put,
            "GET": client_.get,
            "DELETE": client_.delete
        }

        if user_type:  # if user type is present it will over-ride the existing value
            headers.update({common_config.USER_TYPE_KEY: user_type})
        if user:
            headers.update({common_config.USER_KEY: user})
        if hotel_id:
            headers['X-Hotel-Id'] = hotel_id
        print('\n\n' + '#' * 25 + 'REQUEST' + '#' * 25)
        print('REQUEST URL: ' + url + '\nREQUEST TYPE: ' + request_type + '\nHEADERS: ' + str(headers) +
              '\nREQUEST JSON: ' + str(request_json) + '\nREQUEST PARAMS: ' + str(parameters))
        response = client_type.get(request_type)(
            url,
            data=request_json,
            headers=headers
        )
        print('####################### RESPONSE ############################')
        if response.status_code == 204:
            print('RESPONSE CODE: ' + str(response.status_code) + '\nRESPONSE DATA: NO CONTENT')
            assert_(response.status_code, status_code, 'Status code is not matching')
            return response

        else:
            print('RESPONSE CODE: ' + str(response.status_code) + '\nRESPONSE DATA: ' + json.dumps(response.json))
            assert_(response.status_code, status_code, 'Status code is not matching')
            return response.json
