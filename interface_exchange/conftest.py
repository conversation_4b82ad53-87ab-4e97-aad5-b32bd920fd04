import os

import flask
import pytest
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from interface_exchange.app import create_app
from object_registry import finalize_app_initialization

dir_path = os.path.dirname(os.path.abspath(__file__))

pytest_plugins = [
    "tests.interface_exchange.fixtures.conftest_repositories",
    "tests.interface_exchange.fixtures.conftest_interface"
]

_app = create_app()
finalize_app_initialization()


def ensure_valid_test_env_setup(app):
    assert app.config['DEBUG']
    assert app.config['TESTING'], "App Config 'TESTING' must be True for running tests"
    assert os.environ.get('APP_ENV') == 'testing', "APP_ENV should be 'testing' for running tests"
    database_uris = db_engine.get_database_uris()
    assert all(db_creds.dbname == 'interface_exchange_test' for tenant_id, db_creds in
               database_uris.items()), "Database name should be 'interface_exchange_test' for running tests"
    assert all(db_creds.host == 'localhost' for tenant_id, db_creds in
               database_uris.items()), "Database host should be 'localhost' for running tests"


ensure_valid_test_env_setup(_app)



@pytest.fixture(scope="session", autouse=True)
def app():
    ensure_valid_test_env_setup(_app)
    ctx = _app.test_request_context()
    ctx.push()

    print("===========> Dropping and re-creating tables")
    Base.metadata.drop_all(bind=db_engine.get_engine(None))
    Base.metadata.create_all(bind=db_engine.get_engine(None))
    print("=====>>>> Created tables")

    print("===========> Using app fixture")
    yield _app

    print("===========> Teardown app fixture")

    ctx.pop()


# @pytest.fixture(scope="session", autouse=True)
# def seed_data():
#     print("====> seed_data running")
#
#     with open(dir_path + '/integration_tests/resources/seed_data.sql', 'r') as s:
#         db_engine.get_session().execute(s.read())
#     print("Data seeded")
#     db_engine.get_session().commit()


@pytest.fixture(scope="session")
def client_(app, request):
    # test_client = app_.test_client()

    with app.test_client() as client:
        yield client

    while True:
        top = flask._request_ctx_stack.top
        if top is not None and top.preserved:
            top.pop()
        else:
            break

    def teardown():
        pass  # databases and resourses have to be freed at the end. But so far we don't have anything

    request.addfinalizer(teardown)
    return client