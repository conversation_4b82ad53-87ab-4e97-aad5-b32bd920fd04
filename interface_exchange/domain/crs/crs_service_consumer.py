import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import <PERSON>ant<PERSON>lient

from interface_exchange.application.consumers.decorator import consumer_middleware
from interface_exchange.exceptions import InterfaceExchangeException
from interface_exchange.infrastructure.consumers.base_consumer import BaseRMQConsumer
from interface_exchange.infrastructure.consumers.consumer_config import CrsConfig
from interface_exchange.infrastructure.external_clients.crs_client import CrsClient
from interface_exchange.middlewares.request_middleware import exception_handler
from object_registry import locate_instance

logger = logging.getLogger(__name__)


class CrsServiceConsumer(BaseRMQConsumer):
    def __init__(self, key_issuance_application_service, room_stay_service, wakeup_call_service,
                 erp_integration_service, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(CrsConfig(tenant_id))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)
        self.key_issuance_application_service = key_issuance_application_service
        self.room_stay_service = room_stay_service
        self.wakeup_call_service = wakeup_call_service
        self.erp_integration_service = erp_integration_service

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        with current_app.test_request_context():
            consumer_event = body or dict()
            logger.info("CRS process_message called for entity: %s", consumer_event.get('events'))

            if "booking" in consumer_event.get("event_type"):
                self.handle_booking_event(consumer_event)

            if "night_audit" in consumer_event.get("event_type"):
                self.handle_night_audit_event(consumer_event)

            logger.info("CRS message process complete. Message acknowledged")
            message.ack()

    def handle_booking_event(self, booking_event):
        try:
            if not booking_event.get("events"):
                return

            events = booking_event["events"]
            booking_data = [event["payload"] for event in events if event["entity_name"] == 'booking']
            if not booking_data:
                return

            logger.info("CRS processing message for action: %s", booking_event.get("user_action"))
            booking = booking_data[0]
            if booking_event.get("user_action") == 'update_room_stay_dates':
                self.process_booking_extend_message(booking)
            elif booking_event.get("user_action") == 'checkin':
                self.process_guest_checkin_message(booking)
            elif booking_event.get("user_action") == 'checkout':
                self.process_guest_checkout_message(booking)
            elif booking_event.get("user_action") == 'update_room_stay_room_type':
                self.process_guest_room_change(booking)
            elif booking_event.get("user_action") == 'undo_checkin':
                self.process_reverse_checkin_message(booking)
            elif booking_event.get("user_action") == 'undo_checkout':
                self.process_reverse_checkout_message(booking)
            elif booking_event.get("user_action") == 'update_customer_details':
                self.process_updated_customer_details_message(booking)
            else:
                logger.info("Skipping CRS message for action: %s", booking_event.get("user_action"))

        except Exception as exc:
            exception_handler(exc, from_consumer=True)

    def handle_night_audit_event(self, night_audit_event):
        try:
            logger.info("Processing CRS night audit event")
            if not night_audit_event.get("events") or night_audit_event.get('event_type') != 'night_audit.completed':
                return

            events = night_audit_event["events"]
            night_audits = [event['payload'] for event in events if event["entity_name"] == "night_audit"]

            if not night_audits:
                return

            self.process_night_audit_complete_message(night_audits[0])

        except Exception as exc:
            exception_handler(exc, from_consumer=True)

    def process_booking_extend_message(self, booking):
        try:
            booking_id = booking['booking_id']
            hotel_id = booking['hotel_id']
            room_stays = booking['room_stays']

            for room_stay in room_stays:
                if room_stay['room_allocation']:
                    expiry = room_stay['checkout_date']
                    room_id = str(room_stay['room_allocation']['room_id'])
                    keys = self.key_issuance_application_service.get_active_keys(property_id=hotel_id,
                                                                                 booking_id=booking_id, room_id=room_id)
                    for key in keys:
                        self.key_issuance_application_service.update_issued_key(key_issue_id=key.key_issue_id,
                                                                                expiry=expiry)

                    self.room_stay_service.guest_extend_checkout(hotel_id, booking_id, room_stay)
                    self.wakeup_call_service.extend_wakeup_call(hotel_id, booking_id, room_stay)

            logger.info("CRS message - Booking Extend event processed for booking: %s", booking_id)
        except InterfaceExchangeException:
            raise

    def process_guest_checkin_message(self, booking):
        try:
            property_id = booking['hotel_id']
            booking_id = booking['booking_id']
            customer_id_to_details_mapping = self._create_customer_id_name_mapping(booking)

            for room_stay in booking['room_stays']:
                if room_stay['room_allocation']:
                    self.room_stay_service.guest_checkin(property_id, booking_id, room_stay,
                                                         customer_id_to_details_mapping)

            logger.info("CRS message - Guest Checkin event processed for booking: %s", booking_id)
        except InterfaceExchangeException:
            raise

    def process_guest_checkout_message(self, booking):
        try:
            property_id = booking['hotel_id']
            booking_id = booking['booking_id']

            for room_stay in booking['room_stays']:
                if room_stay['room_allocation']:
                    is_new_checkout = self.room_stay_service.guest_checkout(property_id, booking_id, room_stay)
                    if not is_new_checkout:
                        continue

                    if room_stay['status'] == 'checked_out':
                        room_id = str(room_stay['room_allocation']['room_id'])
                        keys = self.key_issuance_application_service.get_active_keys(property_id=property_id,
                                                                                     booking_id=booking_id,
                                                                                     room_id=room_id)
                        for key in keys:
                            self.key_issuance_application_service.expire_key_on_checkout(issued_key=key)

                        if keys:
                            self.key_issuance_application_service.expire_keys(keys)

            logger.info("CRS message - Guest Checkout event processed for booking: %s", booking_id)
        except InterfaceExchangeException:
            raise

    def process_guest_room_change(self, booking):
        try:
            property_id = booking['hotel_id']
            booking_id = booking['booking_id']
            customer_id_to_details_mapping = self._create_customer_id_name_mapping(booking)

            for room_stay in booking['room_stays']:
                if room_stay['room_allocation']:
                    room_change_details = self.room_stay_service.guest_room_change(
                        property_id, booking_id, room_stay, customer_id_to_details_mapping
                    )

                    self.key_issuance_application_service.expire_old_room_keys(property_id, booking_id,
                                                                               room_change_details)

            logger.info("CRS message - Guest room change event processed for booking: %s", booking_id)
        except InterfaceExchangeException:
            raise

    def process_reverse_checkin_message(self, booking):
        try:
            property_id = booking['hotel_id']
            booking_id = booking['booking_id']

            crs_client = locate_instance(CrsClient)
            booking = crs_client.get_booking(booking_id)

            self.room_stay_service.reverse_checkin(booking, property_id)
            room_ids_to_exclude = list()
            for room_stay in booking['room_stays']:
                if room_stay['status'] == 'checked_in' and room_stay['room_allocation']:
                    room_id = str(room_stay['room_allocation']['room_id'])
                    room_ids_to_exclude.append(room_id)

            active_keys = self.key_issuance_application_service.get_all_active_issued_keys(property_id, booking_id)
            keys_to_expire = list()
            for key in active_keys:
                if key.room_id in room_ids_to_exclude:
                    continue

                self.key_issuance_application_service.expire_key_on_checkout(issued_key=key)
                keys_to_expire.append(key)

            if keys_to_expire:
                self.key_issuance_application_service.expire_keys(keys_to_expire)

            logger.info("CRS message - Guest Checkout event processed for booking: %s", booking_id)
        except InterfaceExchangeException:
            raise

    def process_night_audit_complete_message(self, night_audit):
        if self.erp_integration_service is None:
            return

        try:
            self.erp_integration_service.generate_erp_file_details(
                night_audit['hotel_id'],
                night_audit['business_date']
            )

            logger.info("CRS message - ERP file details event processed for night_audit: %s",
                        night_audit['night_audit_id'])
        except InterfaceExchangeException:
            raise

    def process_reverse_checkout_message(self, booking):
        try:
            booking_id = booking['booking_id']
            property_id = booking['hotel_id']
            crs_client = locate_instance(CrsClient)
            booking = crs_client.get_booking(booking_id)
            customer_id_to_details_mapping = self._create_customer_id_name_mapping(booking)

            self.room_stay_service.reverse_checkout(booking, property_id, customer_id_to_details_mapping)
        except InterfaceExchangeException:
            raise

    def process_updated_customer_details_message(self, booking):
        try:
            property_id = booking['hotel_id']
            booking_id = booking['booking_id']
            customer_id_name_mapping = self._create_customer_id_name_mapping(booking)

            for room_stay in booking['room_stays']:
                if room_stay['room_allocation']:
                    self.room_stay_service.guest_detail_update(property_id, booking_id, room_stay,
                                                               customer_id_name_mapping)

        except InterfaceExchangeException:
            raise

    @staticmethod
    def _create_customer_id_name_mapping(booking):
        return {
            customer["customer_id"]: {
                "salutation": customer["salutation"],
                "first_name": customer["first_name"],
                "last_name": customer["last_name"],
                "phone_number": (customer.get("phone") or {}).get("number"),
                "country_code": (customer.get("phone") or {}).get("country_code"),
            }
            for customer in booking["customers"]
        }
