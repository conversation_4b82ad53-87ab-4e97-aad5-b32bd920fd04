# coding=utf-8
"""
Middlewares
"""
import logging
import re

import flask
from flask import current_app

from marshmallow import ValidationError
from treebo_commons.request_tracing.context import get_current_request_id
from werkzeug.exceptions import NotFound

from interface_exchange.api import ApiResponse
from interface_exchange.exceptions import (ExternalClientException,
                                           InterfaceExchangeException,
                                           InvalidBookingId,
                                           PhoneCallSkuNotFoundInCatalog,
                                           ResourceNotFound,
                                           MinibarItemCodeAlreadyExistsException,
                                           InterfaceNameAlreadyExistsException,
                                           RoomBookingNotFoundInCrs,
                                           RoomNotFoundInCatalog,
                                           SkuNotFoundInCatalog,
                                           WiFiSkuNotFoundInCatalog, PolicyAuthException)

logger = logging.getLogger(__name__)


def get_http_status_code_from_exception(exception) -> int:
    status_code_map = {
        InvalidBookingId: 400,
        PhoneCallSkuNotFoundInCatalog: 400,
        ResourceNotFound: 404,
        MinibarItemCodeAlreadyExistsException: 409,
        InterfaceNameAlreadyExistsException: 400,
        RoomBookingNotFoundInCrs: 400,
        WiFiSkuNotFoundInCatalog: 400,
        RoomNotFoundInCatalog: 400,
        SkuNotFoundInCatalog: 400,
        PolicyAuthException: 400,
    }
    return status_code_map.get(type(exception), 500)


def need_error_logging(error):
    if isinstance(error, NotFound):
        return False
    return True


def exception_handler(error, from_consumer=False):
    """
    Exception handler
    :param error:
    :param from_consumer:
    :return:
    """
    # populate status code
    status_code = get_http_status_code_from_exception(error)
    error_code = None

    if isinstance(error, InterfaceExchangeException) or isinstance(error, ExternalClientException):
        error_code = error.code
        error = dict(
            code=error.code,
            message=error.message,
            developer_message=error.description,
            extra_payload=error.extra_payload,
            request_id=get_current_request_id() if not from_consumer else None
        )
        response = ApiResponse.build(errors=[error], status_code=status_code)
    else:
        if getattr(error, "status_code", None):
            status_code = error.status_code
        if getattr(error, "code", None):
            status_code = error.code
            error_code = error.code

        if isinstance(error, ValidationError):
            status_code = 400

        if not re.search(r'^[1-5]\d{2}$', str(status_code)):
            status_code = 500

        error_message = 'Exception occurred.'
        if hasattr(error, 'message'):
            error_message = error.message
        if hasattr(error, 'messages') and isinstance(error.messages, list):
            error_message = error.messages[0] if error.messages else None

        # populate error dict
        error_dict = dict(code=status_code)
        # TODO:: causing JSON serializer error for unknown types. Need to find a cleaner solution for this.
        # error_dict['extra_payload'] = error.args if hasattr(error, 'args') else None
        error_dict['extra_payload'] = dict()
        error_dict['message'] = error_message
        error_dict['developer_message'] = (error.description if hasattr(error, 'description') else str(error)),
        error_dict['request_id'] = get_current_request_id() if not from_consumer else None

        response = ApiResponse.build(errors=[error_dict], status_code=status_code)

    if need_error_logging(error):
        if not from_consumer:
            request = flask.request
            request_url = request.url
            request_headers = dict(request.headers)

            if request.is_json:
                request_data = request.json if request.get_json(silent=True) else request.get_data(as_text=True)
            else:
                request_data = request.get_data(as_text=True)

            logger.exception("Exception in api: %s. Request Payload: %s", error, request_data,
                             extra=dict(error_code=error_code, status_code=status_code, request_url=request_url,
                                        request_headers=request_headers, request_data=request_data,
                                        request_method=request.method))
        else:
            logger.exception("Exception in consumer: %s", error, extra=dict(error_code=error_code))

    return response


def api_validation_exception_handler(exception):
    error = dict(
        code=exception.code,
        message=exception.message,
        developer_message=exception.description,
        extra_payload=exception.extra_payload,
    )
    return ApiResponse.build(errors=[error], status_code=400)
