class PushEventPayloadGeneratorHelper(object):
    @staticmethod
    def build_checkin_push_params(
        booking_id, room_number, guest_name, room_stay_details, guest_stay, share_flag, phone_number=None,
        country_code=None
    ):
        return {
            'booking_id': booking_id,
            'room_number': room_number,
            'guest_name': guest_name,
            'checkin_at': room_stay_details['checkin_date'],
            'checkout_at': room_stay_details['checkout_date'],
            'share_flag': share_flag,
            'room_stay_id': room_stay_details['room_stay_id'],
            'guest_id': guest_stay.guest_id,
            'guest_salutation': guest_stay.salutation,
            'guest_first_name': guest_stay.first_name,
            'guest_last_name': guest_stay.last_name,
            'guest_checkin_at': guest_stay.checkin_at,
            'guest_checkout_at': guest_stay.checkout_at,
            'guest_phone_number': phone_number,
            'country_code': country_code,
        }

    @staticmethod
    def build_checkout_push_event(room_stay, guest_stay, crs_guest_stay):
        return {
            'booking_id': room_stay.booking_id,
            'room_number': room_stay.room_number,
            'share_flag': guest_stay.share_flag,
            'room_stay_id': room_stay.crs_room_stay_id,
            'guest_salutation': guest_stay.salutation,
            'guest_first_name': guest_stay.first_name,
            'guest_last_name': guest_stay.last_name,
            'guest_id': crs_guest_stay['guest_allocation']['guest_id'],
            'guest_checkout_at': crs_guest_stay['actual_checkout_date'],
        }

    @staticmethod
    def build_reverse_checkin_push_event(room_stay, guest_stay, crs_guest_stay):
        return {
            'booking_id': room_stay.booking_id,
            'room_number': room_stay.room_number,
            'share_flag': room_stay.share_flag,
            'room_stay_id': room_stay.crs_room_stay_id,
            'guest_salutation': guest_stay.salutation,
            'guest_first_name': guest_stay.first_name,
            'guest_last_name': guest_stay.last_name,
            'guest_id': crs_guest_stay['guest_allocation']['guest_id'],
            'guest_checkout_at': crs_guest_stay['modified_at'],
        }

    @staticmethod
    def build_reverse_checkout_push_event(
        crs_room_stay, crs_guest_stay, first_guest, guest_stay, room_stay, phone_number=None, country_code=None):
        return {
            'booking_id': room_stay.booking_id,
            'room_number': room_stay.room_number,
            'guest_name': room_stay.guest_name,
            'checkin_at': room_stay.checkin_at.isoformat()
            if room_stay.checkin_at
            else None,
            'checkout_at': room_stay.checkout_at.isoformat()
            if room_stay.checkout_at
            else None,
            'share_flag': 'N' if first_guest else 'Y',
            'room_stay_id': crs_room_stay['room_stay_id'],
            'guest_id': guest_stay.guest_id,
            'guest_salutation': guest_stay.salutation,
            'guest_first_name': guest_stay.first_name,
            'guest_last_name': guest_stay.last_name,
            'guest_checkin_at': crs_guest_stay['actual_checkin_date'],
            'guest_checkout_at': crs_guest_stay['actual_checkin_date'],
            'guest_phone_number': phone_number,
            'country_code': country_code,
        }

    @staticmethod
    def build_guest_details_change_push_event(booking_id, guest_id, guest_stay_data, new_customer_details,
                                              old_guest_stay, room_stay):
        return {
            'booking_id': booking_id,
            'room_number': room_stay.room_number,
            'guest_name': room_stay.guest_name,
            'share_flag': old_guest_stay.share_flag,
            'room_stay_id': room_stay.crs_room_stay_id,
            'guest_id': guest_id,
            'guest_salutation': new_customer_details['salutation'],
            'guest_first_name': new_customer_details['first_name'],
            'guest_last_name': new_customer_details['last_name'],
            'old_guest_salutation': old_guest_stay.salutation,
            'old_guest_first_name': old_guest_stay.first_name,
            'old_guest_last_name': old_guest_stay.last_name,
            'guest_phone_number': new_customer_details.get('phone_number'),
            'country_code': new_customer_details.get('country_code'),
            'modified_at': guest_stay_data['modified_at']
        }

    @staticmethod
    def build_room_change_push_events(
        first_guest,
        guest_stay_data,
        old_room_number,
        room_stay,
        room_stay_details,
        guest_stay,
        guest_details,
    ):
        return {
            "booking_id": room_stay.booking_id,
            "room_number": room_stay.room_number,
            "old_room_number": old_room_number,
            "guest_name": room_stay.guest_name,
            "room_stay_id": room_stay.crs_room_stay_id,
            "share_flag": 'N' if first_guest else 'Y',
            "guest_salutation": guest_stay.salutation,
            "guest_first_name": guest_stay.first_name,
            "guest_last_name": guest_stay.last_name,
            "guest_id": guest_stay_data["guest_allocation"]["guest_id"],
            "room_changed_at": room_stay_details["room_allocation"]["modified_at"],
            "guest_phone_number": guest_details.get("phone_number"),
            "country_code": guest_details.get("country_code"),
        }
