import datetime

from marshmallow.utils import from_iso

from interface_exchange.application.helpers.push_event_payload_generators import PushEvent<PERSON>ayloadGeneratorHelper
from interface_exchange.application.services.dnd_service import DNDService
from interface_exchange.application.services.push_service import PushService
from interface_exchange.application.services.wakeup_service import Wakeup<PERSON>allService
from interface_exchange.constants.crs_constants import BookingStatus
from interface_exchange.core.threadlocal import app_context
from interface_exchange.decorators import session_manager
from interface_exchange.domain.factories.room_stay_factory import \
    RoomStayFactory
from interface_exchange.infrastructure.external_clients.catalog_client import \
    CatalogClient
from interface_exchange.infrastructure.repositories.room_stay_repo import \
    RoomStayRepo
from interface_exchange.domain.factories.guest_stay_factory import GuestStayFactory
from interface_exchange.infrastructure.repositories.guest_stay_repo import Guest<PERSON><PERSON>yRepo
from object_registry import register_instance


@register_instance(
    dependencies=[
        RoomStayRepo,
        CatalogClient,
        PushService,
        DNDService,
        WakeupCallService,
        GuestStayRepo,
    ]
)
class RoomStayService:
    def __init__(
        self,
        room_stay_repo: RoomStayRepo,
        catalog_client: CatalogClient,
        push_service: PushService,
        dnd_service: DNDService,
        wakeup_service: WakeupCallService,
        guest_stay_repo: GuestStayRepo,
    ):
        self.room_stay_repo = room_stay_repo
        self.catalog_client = catalog_client
        self.push_service = push_service
        self.dnd_service = dnd_service
        self.wakeup_service = wakeup_service
        self.guest_stay_repo = guest_stay_repo

    @session_manager(commit=True)
    def guest_checkin(self, property_id, booking_id, room_stay_details, customer_id_to_details_mapping):
        room_id = str(room_stay_details['room_allocation']['room_id'])

        room_stays = self.room_stay_repo.load_room_stays(property_id, booking_id, room_id)
        room_stay_exists = len(room_stays) > 0

        guest = customer_id_to_details_mapping[room_stay_details["guest_stays"][0]["guest_allocation"]["guest_id"]]
        room_number = self.catalog_client.get_room_number(property_id=property_id, room_id=room_id)
        share_flag = 'Y' if len(room_stay_details['guest_stays']) > 1 else 'N'

        if not room_stay_exists:
            room_stay = self.create_room_stay(
                property_id,
                booking_id,
                room_id,
                room_stay_details,
                guest["first_name"],
                room_number,
                share_flag,
            )

        self.push_service.set_push_parameters(property_id)
        guest_id_to_guest_stay_map = self.get_guest_stays_map(booking_id, room_stay_details["room_stay_id"])

        self.process_guest_stays_checkin(
            booking_id,
            room_stay_details,
            guest_id_to_guest_stay_map,
            customer_id_to_details_mapping,
            room_number,
            guest["first_name"],
        )
        app_context.clear_cloud_interfaces()

    @session_manager(commit=True)
    def guest_checkout(self, property_id, booking_id, room_stay_details):
        room_id = str(room_stay_details['room_allocation']['room_id'])

        room_stays = self.room_stay_repo.load_room_stays(property_id, booking_id, room_id)
        if not room_stays:
            return False
        room_stay = room_stays[0]

        if room_stay.has_room_stay_checked_out():
            return False

        room_stay.update_actual_checkout_at(room_stay_details['actual_checkout_date'])
        self.room_stay_repo.update(room_stay)

        self.push_service.set_push_parameters(property_id)
        guest_id_to_guest_stay_map = self.get_guest_stays_map(booking_id, room_stay.crs_room_stay_id)

        first_guest = not any(gd.checkout_at for gd in guest_id_to_guest_stay_map.values())

        for guest_stay_data in room_stay_details["guest_stays"]:
            guest_stay = guest_id_to_guest_stay_map.get(guest_stay_data["guest_allocation"]["guest_id"])
            if guest_stay:
                self.process_guest_checkout(guest_stay_data, guest_stay, room_stay, first_guest)
                first_guest = False
        app_context.clear_cloud_interfaces()
        return True

    @session_manager(commit=True)
    def guest_room_change(self, property_id, booking_id, room_stay_details, customer_id_to_details_mapping):
        room_id = str(room_stay_details['room_allocation']['room_id'])

        room_stays = self.room_stay_repo.load_room_stays(property_id=property_id, booking_id=booking_id,
                                                         search_params={'crs_room_stay_id': str(
                                                             room_stay_details['room_stay_id'])})
        if len(room_stays) == 0:
            return
        room_stay = room_stays[0]

        old_room_id = room_stay.room_id
        old_room_number = room_stay.room_number

        # self.wakeup_service.delete_wakeup_call_for_room_id(booking_id=booking_id, room_id=old_room_id,
        #                                                    property_id=property_id)

        self.dnd_service.delete_dnd_for_room_id(booking_id=booking_id, room_id=old_room_id, property_id=property_id)

        room_id = str(room_stay_details['room_allocation']['room_id'])
        room_number = self.catalog_client.get_room_number(property_id=property_id, room_id=room_id)

        if old_room_number == room_number:
            # Ignore if there is no room change
            return

        room_stay.update_room(room_id, room_number)
        self.room_stay_repo.update(room_stay)

        self.push_service.set_push_parameters(property_id)
        guest_id_to_guest_stay_map = self.get_guest_stays_map(
            booking_id, room_stay.crs_room_stay_id
        )

        first_guest = True
        for guest_stay_data in room_stay_details["guest_stays"]:
            if guest_stay_data["status"] != BookingStatus.CHECKED_IN.value:
                continue
            guest_stay = guest_id_to_guest_stay_map.get(guest_stay_data["guest_allocation"]["guest_id"])
            guest_details = customer_id_to_details_mapping[guest_stay_data["guest_allocation"]["guest_id"]]
            push_params = PushEventPayloadGeneratorHelper.build_room_change_push_events(
                first_guest,
                guest_stay_data,
                old_room_number,
                room_stay,
                room_stay_details,
                guest_stay,
                guest_details
            )
            self.push_service.guest_room_change(push_params, secondary_guest=not first_guest)
            if first_guest:
                first_guest = False
        app_context.clear_cloud_interfaces()

        return {
            'old_room_id': old_room_id,
            'old_room_number': old_room_number,
            'new_room_id': room_id,
            'new_room_number': room_number,
        }

    @session_manager(commit=True)
    def guest_extend_checkout(self, property_id, booking_id, room_stay_details):
        room_id = str(room_stay_details['room_allocation']['room_id'])
        checkout_at = from_iso(room_stay_details['checkout_date'])

        room_stays = self.room_stay_repo.load_room_stays(property_id, booking_id, room_id)
        if len(room_stays) == 0:
            return
        room_stay = room_stays[0]

        if room_stay.checkout_at == checkout_at:
            # Ignore if there is no change in checkout date
            return

        room_stay.update_checkout_at(checkout_at)
        self.room_stay_repo.update(room_stay)

        self.push_service.set_push_parameters(property_id)
        self.push_service.guest_extend_checkout({
            'booking_id': room_stay.booking_id,
            'room_number': room_stay.room_number,
            'checkout_at': room_stay.checkout_at.isoformat(),
        })
        app_context.clear_cloud_interfaces()

    def delete_wakeup_call(self, booking_id):
        self.wakeup_service.delete_wakeup_calls_for_a_booking(booking_id)

    def delete_dnd(self, booking_id):
        self.dnd_service.delete_dnd_for_a_booking_id(booking_id)

    def reverse_checkin(self, booking, property_id):
        room_stays = self.room_stay_repo.load_room_stays(property_id, booking['booking_id'])
        self.delete_wakeup_call(booking['booking_id'])
        self.delete_dnd(booking['booking_id'])
        crs_room_stay_id_to_room_stay_map = {rs.crs_room_stay_id: rs for rs in room_stays}
        for crs_room_stay in booking['room_stays']:
            room_stay = crs_room_stay_id_to_room_stay_map.get(str(crs_room_stay['room_stay_id']))

            if (
                    room_stay
                    and crs_room_stay['status'] in (BookingStatus.RESERVED.value, BookingStatus.CONFIRMED.value,
                                                    BookingStatus.PART_CHECKED_IN.value)
                    and not room_stay.has_room_stay_checked_out()
            ):
                self.push_service.set_push_parameters(property_id)
                guest_id_to_guest_stay_map = self.get_guest_stays_map(
                    booking["booking_id"], room_stay.crs_room_stay_id
                )
                first_guest = True
                for guest_stay_data in crs_room_stay["guest_stays"]:
                    guest_stay = guest_id_to_guest_stay_map.get(str(guest_stay_data["guest_allocation"]["guest_id"]))
                    self.process_guest_stay_during_reverse_checkin(
                        guest_stay, guest_stay_data, room_stay, first_guest
                    )
                    first_guest = False
        app_context.clear_cloud_interfaces()

    @session_manager(commit=True)
    def reverse_checkout(self, booking, property_id, customer_id_to_details_mapping):
        room_stays = self.room_stay_repo.load_room_stays(property_id, booking['booking_id'])
        crs_room_stay_id_to_room_stay_map = {rs.crs_room_stay_id+rs.room_id: rs for rs in room_stays}
        for crs_room_stay in booking['room_stays']:
            room_stay = crs_room_stay_id_to_room_stay_map.get(
                str(crs_room_stay['room_stay_id']) + str(crs_room_stay['room_allocation']['room_id']))
            if (
                    room_stay
                    and crs_room_stay['status'] in (BookingStatus.CHECKED_IN.value,
                                                    BookingStatus.PART_CHECKOUT.value,
                                                    BookingStatus.PART_CHECKED_IN.value)
            ):
                # actual checkout date will be set back to null in case of reverse checkout
                if room_stay.has_room_stay_checked_out():
                    room_stay.update_actual_checkout_at(crs_room_stay['actual_checkout_date'])
                    self.room_stay_repo.update(room_stay)
                self.push_service.set_push_parameters(property_id)

                guest_id_to_guest_stay_map = self.get_guest_stays_map(
                    booking["booking_id"], room_stay.crs_room_stay_id
                )
                for guest_stay_data in crs_room_stay["guest_stays"]:
                    self.process_guest_stay_during_reverse_checkout(
                        guest_id_to_guest_stay_map, guest_stay_data, room_stay, crs_room_stay,
                        customer_id_to_details_mapping
                    )
        app_context.clear_cloud_interfaces()

    @session_manager(commit=True)
    def guest_detail_update(self, property_id, booking_id, room_stay_details, customer_id_to_details_mapping):
        room_id = str(room_stay_details['room_allocation']['room_id'])
        room_stays = self.room_stay_repo.load_room_stays(property_id, booking_id, room_id)
        if not room_stays:
            return
        room_stay = room_stays[0]
        guest_name = self.get_primary_guest_name(customer_id_to_details_mapping, room_stay_details)
        self.push_service.set_push_parameters(property_id)
        if room_stay.guest_name != guest_name:
            room_stay.update_room_stay_guest_name(guest_name)
            self.room_stay_repo.update(room_stay)
        self.check_and_send_guest_detail_update_event(booking_id, room_stay_details, room_stay,
                                                      customer_id_to_details_mapping)
        app_context.clear_cloud_interfaces()

    def check_and_send_guest_detail_update_event(self, booking_id, room_stay_details, room_stay,
                                                 customer_id_to_details_mapping):
        guest_id_to_guest_stay_map = self.get_guest_stays_map(booking_id, room_stay_details['room_stay_id'])
        for guest_stay_data in room_stay_details['guest_stays']:
            if guest_stay_data['guest_allocation']:
                guest_id = guest_stay_data['guest_allocation']['guest_id']
                old_guest_stay = guest_id_to_guest_stay_map.get(guest_id)
                new_customer_details = customer_id_to_details_mapping.get(guest_id)

                if old_guest_stay and new_customer_details:
                    if (
                            old_guest_stay.first_name != new_customer_details['first_name']
                            or old_guest_stay.last_name != new_customer_details['last_name']
                    ):
                        push_params = PushEventPayloadGeneratorHelper.build_guest_details_change_push_event(
                            booking_id, guest_id, guest_stay_data, new_customer_details, old_guest_stay, room_stay)
                        old_guest_stay.update_name(new_customer_details['first_name'],
                                                   new_customer_details['last_name'])
                        self.guest_stay_repo.update(old_guest_stay)
                        self.push_service.send_update_guest_name_event(push_params, secondary_guest=True)

    def get_guest_stays_map(self, booking_id, room_stay_id):
        guest_stays = self.guest_stay_repo.load_guest_stays(
            booking_id=booking_id, room_stay_id=str(room_stay_id)
        )
        return {guest_stay_data.guest_id: guest_stay_data for guest_stay_data in guest_stays}

    def create_room_stay(
        self,
        property_id,
        booking_id,
        room_id,
        room_stay_details,
        guest_name,
        room_number,
        share_flag,
    ):
        room_stay = RoomStayFactory.create_new(
            property_id=property_id,
            booking_id=booking_id,
            room_id=room_id,
            crs_room_stay_id=room_stay_details['room_stay_id'],
            room_number=room_number,
            checkin_at=room_stay_details['checkin_date'],
            checkout_at=room_stay_details['checkout_date'],
            actual_checkin_at=room_stay_details['actual_checkin_date'],
            actual_checkout_at=room_stay_details['actual_checkout_date'],
            guest_name=guest_name,
            share_flag=share_flag,
        )
        self.room_stay_repo.create_room_stay(room_stay)
        return room_stay

    def process_guest_stays_checkin(
        self,
        booking_id,
        room_stay_details,
        guest_id_to_guest_stay_map,
        customer_id_to_details_mapping,
        room_number,
        guest_name,
    ):
        first_guest = not any(guest_stay_details.status == BookingStatus.CHECKED_IN.value
                              for guest_stay_details in guest_id_to_guest_stay_map.values())

        for guest_stay_data in room_stay_details['guest_stays']:
            if guest_stay_data['status'] == BookingStatus.CHECKED_IN.value:
                share_flag = 'N' if first_guest else 'Y'
                guest_id = guest_stay_data['guest_allocation']['guest_id']
                guest_stay = guest_id_to_guest_stay_map.get(guest_id)
                guest_details = customer_id_to_details_mapping[guest_id]
                if guest_stay and guest_stay.status == BookingStatus.CHECKED_IN.value:
                    continue

                if guest_stay:
                    guest_stay.update_status_and_checkin_at(guest_stay_data['actual_checkin_date'],
                                                            BookingStatus.CHECKED_IN.value)
                    self.guest_stay_repo.update(guest_stay)
                else:
                    guest_stay = GuestStayFactory.create_new(
                        booking_id=booking_id,
                        room_stay_id=room_stay_details['room_stay_id'],
                        guest_id=guest_id,
                        salutation=guest_details['salutation'],
                        first_name=guest_details['first_name'],
                        last_name=guest_details['last_name'],
                        checkin_at=guest_stay_data['actual_checkin_date'],
                        checkout_at=guest_stay_data['actual_checkout_date'],
                        status=guest_stay_data['status'],
                        share_flag=share_flag,
                    )
                    self.guest_stay_repo.create_guest_stay(guest_stay)

                push_params = PushEventPayloadGeneratorHelper.build_checkin_push_params(
                    booking_id,
                    room_number,
                    guest_name,
                    room_stay_details,
                    guest_stay,
                    share_flag,
                    phone_number=guest_details.get('phone_number'),
                    country_code=guest_details.get('country_code'),
                )
                self.push_service.guest_checkin(push_params, secondary_guest=not first_guest)
                if first_guest:
                    first_guest = False

    def process_guest_checkout(
        self, crs_guest_stay, guest_stay, room_stay, first_guest
    ):
        if (
            crs_guest_stay['status'] == BookingStatus.CHECKED_OUT.value
            and not guest_stay.has_guest_stay_checked_out()
        ):
            guest_stay.update_status_and_checkout_at(
                crs_guest_stay['actual_checkout_date'], BookingStatus.CHECKED_OUT.value
            )
            self.guest_stay_repo.update(guest_stay)

            push_params = PushEventPayloadGeneratorHelper.build_checkout_push_event(room_stay,
                                                                                             guest_stay,
                                                                                             crs_guest_stay)

            self.push_service.guest_checkout(push_params, secondary_guest=not first_guest)

    @session_manager(commit=True)
    def process_guest_stay_during_reverse_checkin(
        self, guest_stay, crs_guest_stay, room_stay, first_guest
    ):
        if (
            guest_stay
            and crs_guest_stay['status'] == BookingStatus.RESERVED.value
            and not guest_stay.has_guest_stay_checked_out()
        ):
            guest_stay.update_status(BookingStatus.RESERVED.value)
            self.guest_stay_repo.update(guest_stay)

            push_params = PushEventPayloadGeneratorHelper.build_reverse_checkin_push_event(
                room_stay,
                guest_stay,
                crs_guest_stay,
            )

            self.push_service.guest_checkout(push_params, secondary_guest=not first_guest)

    def process_guest_stay_during_reverse_checkout(
        self, guest_id_to_guest_stay_map, crs_guest_stay, room_stay, crs_room_stay, customer_id_to_details_mapping
    ):
        guest_stay = guest_id_to_guest_stay_map.get(str(crs_guest_stay["guest_allocation"]["guest_id"]))
        guest_details = customer_id_to_details_mapping.get(guest_stay.guest_id)
        if (
            guest_stay
            and crs_guest_stay['status'] == BookingStatus.CHECKED_IN.value
            and guest_stay.has_guest_stay_checked_out()
        ):
            first_guest = not any(
                guest_stay_details.status == BookingStatus.CHECKED_IN.value
                for guest_stay_details in guest_id_to_guest_stay_map.values()
            )
            guest_stay.update_status_and_checkout_at(
                 crs_guest_stay['actual_checkout_date'], BookingStatus.CHECKED_IN.value
            )
            self.guest_stay_repo.update(guest_stay)
            push_params = PushEventPayloadGeneratorHelper.build_reverse_checkout_push_event(
                crs_room_stay, crs_guest_stay, first_guest, guest_stay, room_stay, guest_details.get('phone_number'),
                guest_details.get('country_code')
            )
            self.push_service.guest_checkin(push_params, secondary_guest=not first_guest)

    @staticmethod
    def get_primary_guest_name(customer_id_to_details_mapping, room_stay_details):
        customer = customer_id_to_details_mapping[room_stay_details["guest_stays"][0]["guest_allocation"]["guest_id"]]
        if not customer['last_name']:
            guest_name = f"{customer['first_name']}"
        else:
            guest_name = f"{customer['first_name']} {customer['last_name']}"
        return guest_name
