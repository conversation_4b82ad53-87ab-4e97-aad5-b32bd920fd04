class InterfaceExchangeException(Exception):
    error_code = "0001"
    message = "Something went wrong!"

    def __init__(self, description=None, extra_payload=None):
        self.description = description
        self.extra_payload = extra_payload

    def __str__(self):
        return "exception: error_code=%s message=%s description=%s" % (
            self.error_code,
            self.message,
            self.description,
        )

    def with_description(self, description):
        self.description = description
        return self

    @property
    def code(self):
        return "0401" + self.error_code


class ApiValidationException(InterfaceExchangeException):
    error_code = "0006"
    message = "Request Invalid"


class ResourceNotFound(InterfaceExchangeException):
    error_code = "0003"
    message = "Resource not found"

    def __init__(self, resource_name=None, resource_id=None, description=None, extra_payload=None):
        if resource_name and resource_id:
            self.message = f"{resource_name} not found: {resource_id}"
        super(ResourceNotFound, self).__init__(description=description, extra_payload=extra_payload)


class MinibarItemCodeAlreadyExistsException(InterfaceExchangeException):
    error_code = "0004"
    message = "Given minibar item code already exists in the system."


class ExternalClientException(Exception):
    error_code = "0001"
    message = "Something went wrong!"

    def __init__(self, description=None, extra_payload=None):
        self.description = description
        self.extra_payload = extra_payload

    def __str__(self):
        return "exception: error_code=%s message=%s description=%s" % (
            self.error_code,
            self.message,
            self.description,
        )

    def with_description(self, description):
        self.description = description
        return self

    @property
    def code(self):
        return "0501" + self.error_code


class PushClientException(ExternalClientException):
    error_code = "0007"
    message = "Push Service failed!"
    pass


class CatalogClientException(ExternalClientException):
    error_code = "0008"
    message = "Catalog Client Exception!"
    pass


class RoomNotFoundInCatalog(ExternalClientException):
    error_code = "0009"
    message = "Room not found in catalog!"
    pass


class CrsClientException(ExternalClientException):
    error_code = "0010"
    message = "CRS Client Exception!"


class RoomBookingNotFoundInCrs(ExternalClientException):
    error_code = "0011"
    message = "Room booking not found in CRS!"
    pass


class PhoneCallSkuNotFoundInCatalog(ExternalClientException):
    error_code = "0012"
    message = "Phone Call Sku Not Found In Catalog!"


class SkuNotFoundInCatalog(ExternalClientException):
    error_code = "0013"
    message = "SKU Not Found In Catalog!"


class WiFiSkuNotFoundInCatalog(ExternalClientException):
    error_code = "0014"
    message = "WiFi Sku Not Found In Catalog!"


class InvalidBookingId(ExternalClientException):
    error_code = "0015"
    message = "Booking Id is invalid"


class InterfaceNameAlreadyExistsException(InterfaceExchangeException):
    error_code = "0016"
    message = "Interface with this name already exists for the property"


class AuthorizationError(InterfaceExchangeException):
    error_code = "0017"
    message = "You're not authorized to perform this operation"

    def __init__(self, description=None, extra_payload=None):
        super(AuthorizationError, self).__init__(
            description=description, extra_payload=extra_payload
        )


class PolicyAuthException(AuthorizationError):
    error_code = "0017"
    message = "You're not authorized to perform this operation"

    def __init__(self, error=None, message=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(PolicyAuthException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class DownstreamSystemFailure(InterfaceExchangeException):
    error_code = "0018"
    message = "Downstream system failed."

    def __init__(self, message=None, description=None, extra_payload=None):
        self.message = message
        super(DownstreamSystemFailure, self).__init__(
            description=description, extra_payload=extra_payload
        )
