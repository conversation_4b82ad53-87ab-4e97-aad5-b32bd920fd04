name: Integration Tests PR Workflow

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
jobs:
  integration-tests:
    name: Run Integration Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:12
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: interface_exchange_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: |
            ${{ secrets.SSH_TREEBO_COMMONS_PRIVATE_KEY }}
            ${{ secrets.SSH_FLASKHEALTHCHECK_PRIVATE_KEY }}
            ${{ secrets.SSH_THSC_PRIVATE_KEY }}

      - name: Set up Python 3.8
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'
          cache: 'pip'
          cache-dependency-path: 'requirements/deploy.txt'

      - name: Install dependencies
        run: |
          pip install pip==23.0.1
          TREEBO_COMMONS_VERSION=$(grep "treebo-commons==" requirements/deploy.txt | cut -d'=' -f3)
          THSC_VERSION=$(grep "thsc==" requirements/deploy.txt | cut -d'=' -f3)
          FLASKHEALTHCHECK_VERSION=$(grep "flaskhealthcheck==" requirements/deploy.txt | cut -d'=' -f3)
          cat << EOF > requirements/deploy.txt
          git+ssh://**************/treebo-noss/flaskhealthcheck.git@v$FLASKHEALTHCHECK_VERSION#egg=flaskhealthcheck
          git+ssh://**************/treebo-noss/treebo-common.git@v$TREEBO_COMMONS_VERSION#egg=treebo-commons
          git+ssh://**************/treebo-noss/prometheus.git@main#egg=thsc
          -r base.txt
          EOF
          # Install all dependencies
          pip install -r requirements/dev.txt

      # Run integration tests using a common pattern
      - name: Run integration tests
        run: |
          # Create an array of test directories
          TEST_DIRS=(
            "tokens_v2"
            "interface_v2"
            "interface_configs"
          )

          # Loop through each test directory and run tests
          for dir in "${TEST_DIRS[@]}"; do
            echo "\n\n==== Running tests for $dir ===="
            python -m pytest interface_exchange/integration_tests/tests/$dir/tests/ -v

            # Exit if tests fail
            if [ $? -ne 0 ]; then
              echo "Tests for $dir failed!"
              exit 1
            fi
          done

      - name: Generate coverage report
        run: |
          python -m pytest --cov=interface_exchange.api.tokens_v2 --cov=interface_exchange.api.interface_v2 --cov=interface_exchange.api.interface_configs interface_exchange/integration_tests/tests/tokens_v2/tests/ interface_exchange/integration_tests/tests/interface_v2/tests/ interface_exchange/integration_tests/tests/interface_configs/tests/ --cov-report=xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          fail_ci_if_error: true
