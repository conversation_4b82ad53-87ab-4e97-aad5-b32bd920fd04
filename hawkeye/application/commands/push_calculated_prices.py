#!/usr/bin/env python

import logging
import time

import click
from hawkeye.infrastructure.database.db_engine import setup_tenant_sessions
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.utils import dateutils

from hawkeye.app import create_app
from hawkeye.application.services.price_push_event_service import PricePushEventService
from hawkeye.globals import worker_context
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should run.",
    default=TenantClient.get_default_tenant(),
)
@inject(price_push_event_service=PricePushEventService)
def trigger_price_push_process(price_push_event_service, tenant_id=TenantClient.get_default_tenant()):
    click.echo(f"Tenant ID: {tenant_id}")
    setup_tenant_sessions(tenant_id=tenant_id)
    worker_context.set_tenant_id(tenant_id)

    while True:
        try:
            price_push_event_service.publish_unpublished_prices_to_queue()
            time.sleep(5)
        except Exception as ex:
            logger.exception('Error while publishing prices to queue')
            logger.exception(ex)
            time.sleep(10)


if __name__ == '__main__':
    app = create_app()
    trigger_price_push_process()
