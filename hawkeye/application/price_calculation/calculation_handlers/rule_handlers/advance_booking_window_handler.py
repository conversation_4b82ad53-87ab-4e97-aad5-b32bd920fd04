import datetime
from hawkeye.application.price_calculation.calculation_handlers.rule_handlers.base_rule_handler import BaseRuleHandler
from hawkeye.constants.hawkeye_constant import RuleType
from object_registry import register_instance
from treebo_commons.utils import dateutils


@register_instance()
class AdvanceBookingWindowHandler(BaseRuleHandler):

    @property
    def rule_type(self):
        return RuleType.ADVANCE_BOOKING_WINDOW

    def get_price_rule(self, price_aggregate, price_rules):
        advance_booking_window = (price_aggregate.target_date - dateutils.current_date()).days
        applicable_price_rules = price_rules.get(self.rule_type.value, [])
        return next(
            (
                price_rule for price_rule in applicable_price_rules
                if price_rule.config.abw_start <= advance_booking_window <= price_rule.config.abw_end
            ),
            None
        )
