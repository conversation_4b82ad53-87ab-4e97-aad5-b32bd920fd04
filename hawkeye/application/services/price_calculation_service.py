import logging
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List

import newrelic.agent
from treebo_commons.utils import dateutils

from hawkeye.application.decorators import request_middleware
from hawkeye.application.decorators import session_manager
from hawkeye.application.price_calculation.calculation_handlers.calculate_incremental_price import (
    CalculateIncrementalPriceCommandHandler,
)
from hawkeye.application.price_calculation.calculation_handlers.calculate_rule_based_price import (
    CalculateRuleBasedPriceCommandHandler,
)
from hawkeye.application.price_calculation.calculation_handlers.calculate_rule_based_sku_price import (
    CalculateRuleBasedSkuPriceCommandHandler,
)
from hawkeye.common.fix_inconsistent_inventories import fix_inconsistent_inventories
from hawkeye.constants.hawkeye_constant import SkuNameComponent
from hawkeye.domain.entities.price_calculation_event import PriceCalculationEvent
from hawkeye.domain.entities.price_calculation_trigger import PriceCalculationTrigger
from hawkeye.domain.price_calculation.aggregates.sku_price_aggregate import SkuPriceAggregate
from hawkeye.domain.price_calculation.factories.price_factory import PriceFactory
from hawkeye.infrastructure.exception import ValidationException
from hawkeye.infrastructure.publisher.price_calculation_publisher import PriceCalculationPublisher
from hawkeye.infrastructure.repositories.base_room_type_price_rule_repository import (
    BaseRoomTypePriceRuleRepository,
)
from hawkeye.infrastructure.repositories.base_room_type_sku_price_rule_repository import (
    BaseRoomTypeSkuPriceRuleRepository
)
from hawkeye.infrastructure.repositories.competitive_price_weightage_repository import (
    CompetitivePriceWeightageRepository
)
from hawkeye.infrastructure.repositories.compset_pricing_threshold_repository import (
    CompsetPricingThresholdRepository
)
from hawkeye.infrastructure.repositories.hotel_config_repository import (
    HotelConfigRepository
)
from hawkeye.infrastructure.repositories.hotel_inventory_repository import (
    HotelRoomTypeInventoryRepository
)
from hawkeye.infrastructure.repositories.hotel_room_type_repository import (
    HotelRoomTypeRepository
)
from hawkeye.infrastructure.repositories.hotel_sku_repository import HotelSkuRepository
from hawkeye.infrastructure.repositories.incremental_price_rule_repository import (
    IncrementalPriceRuleRepository,
)
from hawkeye.infrastructure.publisher.price_alert_publisher import PriceAlertPublisher
from hawkeye.infrastructure.repositories.price_alert_repository import PriceAlertRepository
from hawkeye.infrastructure.repositories.price_calculation_trigger_repository import PriceCalculationTriggerRepository
from hawkeye.infrastructure.repositories.price_update_config_repository import (
    PriceUpdateConfigRepository,
)
from hawkeye.infrastructure.repositories.room_type_price_repository import (
    RoomTypePriceRepository
)
from hawkeye.utils.collectionutils import chunks
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[
    CalculateRuleBasedPriceCommandHandler,
    CalculateIncrementalPriceCommandHandler,
    RoomTypePriceRepository,
    HotelConfigRepository,
    HotelRoomTypeInventoryRepository,
    HotelRoomTypeRepository,
    PriceCalculationPublisher,
    BaseRoomTypePriceRuleRepository,
    CompetitivePriceWeightageRepository,
    CompsetPricingThresholdRepository,
    PriceUpdateConfigRepository,
    IncrementalPriceRuleRepository,
    PriceCalculationTriggerRepository,
    BaseRoomTypeSkuPriceRuleRepository,
    HotelSkuRepository,
    CalculateRuleBasedSkuPriceCommandHandler,
    PriceAlertPublisher,
    PriceAlertRepository,
])
class PriceCalculationService:
    def __init__(
        self,
        rule_based_price_calculation_handler: CalculateRuleBasedPriceCommandHandler,
        incremental_price_rule_handler: CalculateIncrementalPriceCommandHandler,
        room_type_price_repository: RoomTypePriceRepository,
        hotel_config_repository: HotelConfigRepository,
        hotel_inventory_repository: HotelRoomTypeInventoryRepository,
        hotel_room_type_repository: HotelRoomTypeRepository,
        price_calculation_publisher: PriceCalculationPublisher,
        base_room_type_price_rule_repository: BaseRoomTypePriceRuleRepository,
        competitive_price_weightage_repository: CompetitivePriceWeightageRepository,
        compset_pricing_threshold_repository: CompsetPricingThresholdRepository,
        price_update_config_repository: PriceUpdateConfigRepository,
        incremental_price_rule_repository: IncrementalPriceRuleRepository,
        price_calculation_trigger_repository: PriceCalculationTriggerRepository,
        base_room_type_sku_price_rule_repository: BaseRoomTypeSkuPriceRuleRepository,
        hotel_sku_repository: HotelSkuRepository,
        rule_based_sku_price_calculation_handler: CalculateRuleBasedSkuPriceCommandHandler,
        price_alert_publisher: PriceAlertPublisher,
        price_alert_repository: PriceAlertRepository,
    ):
        self.rule_based_price_calculation_handler = rule_based_price_calculation_handler
        self.incremental_price_rule_handler = incremental_price_rule_handler
        self.room_type_price_repository = room_type_price_repository
        self.hotel_config_repository = hotel_config_repository
        self.hotel_inventory_repository = hotel_inventory_repository
        self.hotel_room_type_repository = hotel_room_type_repository
        self.price_calculation_publisher = price_calculation_publisher
        self.base_room_type_price_rule_repository = base_room_type_price_rule_repository
        self.competitive_price_weightage_repository = competitive_price_weightage_repository
        self.compset_pricing_threshold_repository = compset_pricing_threshold_repository
        self.price_update_config_repository = price_update_config_repository
        self.incremental_price_rule_repository = incremental_price_rule_repository
        self.price_calculation_trigger_repository = price_calculation_trigger_repository
        self.base_room_type_sku_price_rule_repository = base_room_type_sku_price_rule_repository
        self.hotel_sku_repository = hotel_sku_repository
        self.rule_based_sku_price_calculation_handler = rule_based_sku_price_calculation_handler
        self.price_alert_publisher = price_alert_publisher
        self.price_alert_repository = price_alert_repository

    @staticmethod
    def _get_hotel_wise_price_rules(repository, hotel_ids=None):
        all_price_rules = repository.load_all(hotel_ids=hotel_ids)
        hotel_wise_price_rules = defaultdict(list)
        for price_rule in all_price_rules:
            hotel_wise_price_rules[price_rule.hotel_id].append(price_rule)
        return hotel_wise_price_rules

    def _get_hotel_wise_incremental_price_rules(self, hotel_ids=None):
        incremental_price_rules = self.incremental_price_rule_repository.load_all(hotel_ids=hotel_ids)
        hotel_wise_incremental_price_rules = defaultdict(list)
        for price_rule in incremental_price_rules:
            hotel_wise_incremental_price_rules[price_rule.hotel_id].append(price_rule)
        return hotel_wise_incremental_price_rules

    def _get_hotel_wise_competitive_price_weightages(self, hotel_ids=None):
        competitive_price_weightages = self.competitive_price_weightage_repository.load_all(hotel_ids=hotel_ids)
        hotel_wise_competitive_price_weightages = defaultdict(list)
        for price_weightage in competitive_price_weightages:
            hotel_wise_competitive_price_weightages[price_weightage.hotel_id].append(price_weightage)
        return hotel_wise_competitive_price_weightages

    def _get_hotel_wise_compset_pricing_thresholds(self, hotel_ids=None):
        compset_pricing_thresholds = self.compset_pricing_threshold_repository.load_all(hotel_ids=hotel_ids)
        hotel_wise_compset_pricing_thresholds = defaultdict(list)
        for compset_pricing_threshold in compset_pricing_thresholds:
            hotel_wise_compset_pricing_thresholds[compset_pricing_threshold.hotel_id].append(compset_pricing_threshold)
        return hotel_wise_compset_pricing_thresholds

    def _run_price_calculation_based_on_custom_abw(
        self,
        price_calculation_trigger,
        enabled_hotel_configs,
        hotel_wise_price_rules,
        hotel_wise_incremental_price_rules,
        hotel_wise_sku_price_rules,
        hotel_wise_competitive_price_weightages,
        hotel_wise_compset_pricing_thresholds,
    ):
        self.save_price_calculation_trigger(price_calculation_trigger)
        for hotel_config in enabled_hotel_configs:
            base_room_type_price_rules = hotel_wise_price_rules.get(hotel_config.hotel_id, [])
            base_room_type_sku_price_rules = hotel_wise_sku_price_rules.get(hotel_config.hotel_id, [])
            incremental_price_rules = hotel_wise_incremental_price_rules.get(hotel_config.hotel_id, [])
            competitive_price_weightages = hotel_wise_competitive_price_weightages.get(hotel_config.hotel_id, [])
            compset_pricing_thresholds = hotel_wise_compset_pricing_thresholds.get(hotel_config.hotel_id, [])
            if not hotel_config.is_competitor_price_enabled:
                competitive_price_weightages = []
                compset_pricing_thresholds = []

            self.price_calculation_publisher.publish(
                PriceCalculationEvent(
                    hotel_id=hotel_config.hotel_id,
                    abw_start=price_calculation_trigger.abw_start,
                    abw_end=price_calculation_trigger.abw_end,
                    base_room_type_price_rules=[
                        base_room_type_price_rule.to_dict()
                        for base_room_type_price_rule in base_room_type_price_rules
                    ],
                    base_room_type_sku_price_rules=[
                        base_room_type_sku_price_rule.to_dict()
                        for base_room_type_sku_price_rule in base_room_type_sku_price_rules
                    ],
                    incremental_price_rules=[rule.to_json() for rule in incremental_price_rules],
                    competitive_price_weightages=[rule.to_dict() for rule in competitive_price_weightages],
                    compset_pricing_thresholds=[threshold.to_dict() for threshold in compset_pricing_thresholds],
                    price_trigger_id=price_calculation_trigger.id,
                )
            )

    def _run_price_calculation_based_on_config_time(
        self,
        config_date_time,
        price_calculation_trigger,
        enabled_hotel_configs,
        hotel_wise_price_rules,
        hotel_wise_incremental_price_rules,
        hotel_wise_sku_price_rules,
        hotel_wise_competitive_price_weightages,
        hotel_wise_compset_pricing_thresholds,
    ):
        price_update_configs = self.price_update_config_repository.load_all_for_current_time(hour=config_date_time.hour,
                                                                                             minute=0)
        if not price_update_configs:
            return

        price_update_config_ids = []
        price_calculation_events = []
        for hotel_config in enabled_hotel_configs:
            for price_update_config in price_update_configs:
                price_update_config_ids.append(str(price_update_config.id))
                base_room_type_price_rules = hotel_wise_price_rules.get(hotel_config.hotel_id, [])
                base_room_type_sku_price_rules = hotel_wise_sku_price_rules.get(hotel_config.hotel_id, [])
                incremental_price_rules = hotel_wise_incremental_price_rules.get(hotel_config.hotel_id, [])
                competitive_price_weightages = hotel_wise_competitive_price_weightages.get(hotel_config.hotel_id, [])
                compset_pricing_thresholds = hotel_wise_compset_pricing_thresholds.get(hotel_config.hotel_id, [])
                if not hotel_config.is_competitor_price_enabled:
                    competitive_price_weightages = []
                    compset_pricing_thresholds = []

                price_calculation_events.append(
                    PriceCalculationEvent(
                        hotel_id=hotel_config.hotel_id,
                        abw_start=price_update_config.abw_start,
                        abw_end=price_update_config.abw_end,
                        base_room_type_price_rules=[
                            base_room_type_price_rule.to_dict()
                            for base_room_type_price_rule in base_room_type_price_rules
                        ],
                        base_room_type_sku_price_rules=[
                            base_room_type_sku_price_rule.to_dict()
                            for base_room_type_sku_price_rule in base_room_type_sku_price_rules
                        ],
                        incremental_price_rules=[rule.to_json() for rule in incremental_price_rules],
                        competitive_price_weightages=[rule.to_dict() for rule in competitive_price_weightages],
                        compset_pricing_thresholds=[threshold.to_dict() for threshold in compset_pricing_thresholds],
                        price_trigger_id=price_calculation_trigger.id,
                    )
                )
        self.save_price_calculation_trigger(price_calculation_trigger, price_update_config_ids)

        for price_calculation_event in price_calculation_events:
            self.price_calculation_publisher.publish(
                price_calculation_event
            )

    @request_middleware
    @newrelic.agent.background_task(name='trigger_price_calculation_process')
    def trigger_price_calculation(self, config_date_time, price_calculation_trigger: PriceCalculationTrigger):
        hotel_ids = set(price_calculation_trigger.hotel_ids.split(",")) if price_calculation_trigger.hotel_ids else None
        hotel_wise_price_rules = self._get_hotel_wise_price_rules(
            self.base_room_type_price_rule_repository,
            hotel_ids=hotel_ids
        )
        hotel_wise_sku_price_rules = self._get_hotel_wise_price_rules(
            self.base_room_type_sku_price_rule_repository,
            hotel_ids=hotel_ids,
        )
        hotel_wise_incremental_price_rules = self._get_hotel_wise_incremental_price_rules(hotel_ids=hotel_ids)
        enabled_hotel_configs = self.hotel_config_repository.load_all_enabled(hotel_ids=hotel_ids)
        hotel_wise_competitive_price_weightages = self._get_hotel_wise_competitive_price_weightages(hotel_ids=hotel_ids)
        hotel_wise_compset_pricing_thresholds = self._get_hotel_wise_compset_pricing_thresholds(hotel_ids=hotel_ids)

        if price_calculation_trigger.abw_start is not None and price_calculation_trigger.abw_end is not None:
            self._validate_hotel_ids_for_manual_trigger(hotel_ids, enabled_hotel_configs)
            self._run_price_calculation_based_on_custom_abw(
                price_calculation_trigger,
                enabled_hotel_configs,
                hotel_wise_price_rules,
                hotel_wise_incremental_price_rules,
                hotel_wise_sku_price_rules,
                hotel_wise_competitive_price_weightages,
                hotel_wise_compset_pricing_thresholds,
            )
            return
        self._run_price_calculation_based_on_config_time(
            config_date_time,
            price_calculation_trigger,
            enabled_hotel_configs,
            hotel_wise_price_rules,
            hotel_wise_incremental_price_rules,
            hotel_wise_sku_price_rules,
            hotel_wise_competitive_price_weightages,
            hotel_wise_compset_pricing_thresholds,
        )

    @session_manager(commit=True)
    def calculate_rule_based_prices(
            self, hotel_id, abw_start, abw_end, price_trigger_id, base_room_type_price_rules, incremental_price_rules,
            sku_price_rules, competitive_price_weightages, compset_pricing_thresholds,
    ):
        current_datetime = dateutils.current_datetime()
        start_date = dateutils.add(current_datetime, abw_start)
        end_date = dateutils.add(current_datetime, abw_end)
        room_type_entities = self.hotel_room_type_repository.load_hotel_room_types(hotel_id)
        hotel_room_inventories = self.hotel_inventory_repository.load_hotel_room_inventories(
            hotel_id,
            start_date.isoformat(),
            end_date.isoformat(),
        )
        hotel_sku_entities = self.hotel_sku_repository.load_all_hotel_skus(hotel_id)
        extra_adult_rack_rate, extra_adult_sku_codes, sku_name_wise_sku_entities = self._build_sku_info(
            hotel_sku_entities
        )
        logger.info(
            f"Time Taken to collect-data {(dateutils.current_datetime() - current_datetime).total_seconds():.2f} "
            f"seconds"
        )
        current_datetime = dateutils.current_datetime()
        extra_adult_sku_price_aggregates, sku_price_aggregates = PriceFactory.create_sku_price_aggregates(
            hotel_id,
            start_date,
            end_date,
            price_trigger_id,
            sku_price_rules,
            room_type_entities,
            sku_name_wise_sku_entities,
            hotel_room_inventories,
        )
        logger.info(
            f"Time Taken to build aggregates {(dateutils.current_datetime() - current_datetime).total_seconds():.2f} seconds"
        )
        self._calculate_extra_adult_sku_prices(extra_adult_sku_price_aggregates)
        extra_adult_sku_prices = self.room_type_price_repository.load_sku_prices_for_date_range(
            hotel_id, start_date.date(), end_date.date(), extra_adult_sku_codes
        )
        price_aggregates = PriceFactory.create_base_room_type_prices(
            hotel_id,
            start_date,
            end_date,
            price_trigger_id,
            base_room_type_price_rules,
            room_type_entities,
            hotel_room_inventories,
            incremental_price_rules,
            competitive_price_weightages,
            compset_pricing_thresholds,
        )
        self._calculate_rule_based_room_prices(price_aggregates)
        price_alerts = self._save_price_alerts(hotel_id, price_aggregates)
        self._publish_price_alerts(price_alerts)
        self._calculate_incremental_room_prices(
            price_aggregates,
            extra_adult_sku_prices,
            extra_adult_rack_rate,
        )
        room_type_prices = self.room_type_price_repository.load_room_price_for_date_range(
            hotel_id, start_date.date(), end_date.date()
        )
        current_datetime = dateutils.current_datetime()
        self._calculate_sku_prices(sku_price_aggregates, room_type_prices)
        logger.info(
            f"Time Taken to calc sku_prices {(dateutils.current_datetime() - current_datetime).total_seconds():.2f} "
            f"seconds"
        )

    @staticmethod
    def _build_sku_info(hotel_sku_entities):
        sku_name_wise_sku_entities, extra_adult_sku_codes, extra_adult_rack_rate = {}, [], {}
        for sku in hotel_sku_entities:
            sku_name = sku.sku_name.upper()
            sku_name_wise_sku_entities[sku_name] = sku
            if SkuNameComponent.EXTRA_ADULT in sku_name:
                extra_adult_sku_codes.append(sku.sku_code)
                extra_adult_rack_rate[sku_name] = float(sku.rack_rate)
        return extra_adult_rack_rate, extra_adult_sku_codes, sku_name_wise_sku_entities

    def calculate_rule_based_price_for_price_alert(self, price_alert):
        hotel_id = price_alert.hotel_id
        hotel_wise_price_rules = self._get_hotel_wise_price_rules(
            self.base_room_type_price_rule_repository,
            hotel_ids=[hotel_id],
        )
        hotel_wise_incremental_price_rules = self._get_hotel_wise_incremental_price_rules(hotel_ids=[hotel_id])
        hotel_wise_competitive_price_weightages = self._get_hotel_wise_competitive_price_weightages(hotel_ids=[hotel_id])
        hotel_wise_compset_pricing_thresholds = self._get_hotel_wise_compset_pricing_thresholds(hotel_ids=[hotel_id])
        base_room_type_price_rules = hotel_wise_price_rules.get(hotel_id, [])
        incremental_price_rules = hotel_wise_incremental_price_rules.get(hotel_id, [])
        competitive_price_weightages = hotel_wise_competitive_price_weightages.get(hotel_id, [])
        compset_pricing_thresholds = hotel_wise_compset_pricing_thresholds.get(hotel_id, [])
        room_type_entities = self.hotel_room_type_repository.load_hotel_room_types(hotel_id)
        hotel_room_inventories = self.hotel_inventory_repository.load_hotel_room_inventories(
            hotel_id,
            price_alert.stay_date.isoformat(),
            price_alert.stay_date.isoformat(),
        )
        price_aggregates = PriceFactory.create_base_room_type_prices(
            price_alert.hotel_id,
            dateutils.datetime_at_midnight(price_alert.stay_date),
            dateutils.datetime_at_midnight(price_alert.stay_date),
            price_alert.id,
            base_room_type_price_rules,
            room_type_entities,
            hotel_room_inventories,
            incremental_price_rules,
            competitive_price_weightages,
            compset_pricing_thresholds,
        )
        self._calculate_rule_based_room_prices(price_aggregates)
        return price_aggregates[0].price_alert

    def _calculate_rule_based_room_prices(
        self,
        price_aggregates,
    ):
        def process_price_aggregate(room_price_aggregate):
            if room_price_aggregate.has_inconsistent_room_inventory():
                room_price_aggregate = fix_inconsistent_inventories(price_aggregate=room_price_aggregate)
            self.rule_based_price_calculation_handler.handle(room_price_aggregate)

        with ThreadPoolExecutor() as executor:
            [
                executor.submit(
                    process_price_aggregate, aggregate
                )
                for aggregate in price_aggregates
            ]

    @session_manager(commit=True)
    def _save_price_alerts(
        self,
        hotel_id,
        price_aggregates,
    ):
        price_alerts = [price_aggregate.price_alert for price_aggregate in price_aggregates if price_aggregate.price_alert]
        if not price_alerts:
            return []
        stay_dates = [price_alert.stay_date for price_alert in price_alerts]
        existing_price_alerts = self.price_alert_repository.load_price_alerts(hotel_id, stay_dates)
        stay_date_to_existing_price_alerts = {price_alert.stay_date: price_alert for price_alert in existing_price_alerts}
        price_alerts_to_expire = []
        price_alerts_to_save = []
        for price_alert in price_alerts:
            existing_price_alert = stay_date_to_existing_price_alerts.get(price_alert.stay_date)
            if not existing_price_alert:
                price_alerts_to_save.append(price_alert)
                continue
            if existing_price_alert != price_alert:
                existing_price_alert.is_expired = True
                price_alerts_to_expire.append(existing_price_alert)
                price_alerts_to_save.append(price_alert)
        self.price_alert_repository.update_all(price_alerts_to_expire)
        return self.price_alert_repository.save_all(price_alerts_to_save)

    def _publish_price_alerts(self, price_alerts):
        for price_alert in price_alerts:
            self.price_alert_publisher.publish(price_alert)

    @session_manager(commit=True)
    def _calculate_incremental_room_prices(
            self,
            price_aggregates,
            extra_adult_sku_prices,
            extra_adult_rack_rate,
    ):
        calculated_prices = list()

        def process_price_aggregate(room_price_aggregate, extra_adult_sku_prices, extra_adult_rack_rate):
            self.incremental_price_rule_handler.handle(
                room_price_aggregate, extra_adult_sku_prices, extra_adult_rack_rate
            )

            return room_price_aggregate.room_type_price_entity, room_price_aggregate.incremental_price_entities

        with ThreadPoolExecutor() as executor:
            futures = [
                executor.submit(
                    process_price_aggregate, aggregate, extra_adult_sku_prices[aggregate.target_date],
                    extra_adult_rack_rate
                )
                for aggregate in price_aggregates
            ]

            for future in futures:
                room_type_price_entity, incremental_price_entities = future.result()
                calculated_prices.append(room_type_price_entity)
                calculated_prices.extend(incremental_price_entities)

        self.room_type_price_repository.update_all(calculated_prices)

    def _calculate_extra_adult_sku_prices(self, price_aggregates):
        def process_extra_adult_sku(extra_adult_sku_price_aggregate):
            self.rule_based_sku_price_calculation_handler.handle_extra_adult_skus(extra_adult_sku_price_aggregate)
            return extra_adult_sku_price_aggregate.room_type_price_entity

        calculated_prices = list()
        with ThreadPoolExecutor() as executor:
            extra_adult_futures = [
                executor.submit(process_extra_adult_sku, aggregate)
                for aggregate in price_aggregates
            ]

        for future in extra_adult_futures:
            calculated_prices.append(future.result())
        self.room_type_price_repository.bulk_save_or_update(calculated_prices)

    def _calculate_sku_prices(self, price_aggregates: List[SkuPriceAggregate], room_type_prices):

        def process_base_room_type_sku(price_aggregate: SkuPriceAggregate, room_type_price):
            self.rule_based_sku_price_calculation_handler.handle(
                price_aggregate, room_type_price
            )
            return price_aggregate.room_type_price_entity

        batch_size = 1000
        calculated_prices = []

        for chunk in chunks(price_aggregates, batch_size):

            with ThreadPoolExecutor() as executor:
                futures = [
                    executor.submit(
                        process_base_room_type_sku,
                        agg,
                        room_type_prices[f"{agg.target_date}_{agg.room_sku_name}"],
                    )
                    for agg in chunk
                    if f"{agg.target_date}_{agg.room_sku_name}" in room_type_prices
                ]
            calculated_prices.extend([future.result() for future in futures])
        if calculated_prices:
            self.room_type_price_repository.bulk_save_or_update(calculated_prices)

    def validate_hotel_can_be_enabled(self, hotel_id):
        errors = []

        hotel = self.hotel_config_repository.load(hotel_id)
        if not hotel.is_live:
            errors.append("Hotel should be live before it can be enabled.")

        hotel_room_types = self.hotel_room_type_repository.load_hotel_room_types(hotel_id)
        incremental_price_rule_room_types = set([
            rule.room_type for rule in self.incremental_price_rule_repository.load_all([hotel_id]) if not rule.is_deleted
        ])

        base_room_types = 0
        for hotel_room_type in hotel_room_types:
            if hotel_room_type.is_base_room_type:
                base_room_types += 1
            elif hotel_room_type.room_type_name not in incremental_price_rule_room_types:
                errors.append(f"Price upgrade rule is not configured for type {hotel_room_type.room_type_name}")
            if not hotel_room_type.total_rooms:
                errors.append(f"Total room count should be non-zero for type {hotel_room_type.room_type_name}")

        if base_room_types != 1:
            errors.append("At-least one and only one base room type should be configured.")

        return errors

    @session_manager(commit=True)
    def save_price_calculation_trigger(
            self, price_calculation_trigger: PriceCalculationTrigger, price_update_config_ids=None
    ):
        if price_update_config_ids:
            price_calculation_trigger.price_update_config_id = ",".join(set(price_update_config_ids))
        self.price_calculation_trigger_repository.create(price_calculation_trigger)

    @staticmethod
    def _validate_hotel_ids_for_manual_trigger(hotel_ids, enabled_hotel_configs):
        invalid_hotel_ids = set(hotel_ids) - {hotel_config.hotel_id for hotel_config in enabled_hotel_configs}
        if invalid_hotel_ids:
            raise ValidationException(description=f"Price push is not triggerd as following hotel are invalid or not "
                                                  f"enabled. Hotel Ids{invalid_hotel_ids}")

    @request_middleware
    @session_manager(commit=True)
    def re_publish_price(self, hotel_id, start_date, end_date):
        base_room_prices = self.room_type_price_repository.load_bulk_for_re_publish(hotel_id, start_date, end_date)
        abw_window = dateutils.ymd_str_to_date(end_date) - dateutils.ymd_str_to_date(start_date)
        if not (0 <= abw_window.days < 31):
            raise ValueError("ABW end should be greater than ABW start and not more than 30 from ABW start")
        for base_room_price in base_room_prices:
            base_room_price.mark_unpublished()
        self.room_type_price_repository.update_all(base_room_prices)
