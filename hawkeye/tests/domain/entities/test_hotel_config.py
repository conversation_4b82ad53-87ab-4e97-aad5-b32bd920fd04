import pytest

from hawkeye.application.services.dtos.catalog_dtos import CatalogHotelDto
from hawkeye.domain.entities.hotel_config import HotelConfig


@pytest.fixture()
def hotel_config():
    return HotelConfig("sample_hotel_id")


def test_update_status_from_dto(hotel_config):
    catalog_hotel_dto = CatalogHotelDto.create_from_catalog_data({"id": "sample_hotel_id", "status": "LIVE"})
    hotel_config.update_status_from_dto(catalog_hotel_dto)
    assert hotel_config.is_live


def test_update_status_from_dto_if_status_not_live(hotel_config):
    catalog_hotel_dto = CatalogHotelDto.create_from_catalog_data({"id": "sample_hotel_id", "status": "NEAR_CONFIRMED"})
    hotel_config.update_status_from_dto(catalog_hotel_dto)
    assert not hotel_config.is_live
