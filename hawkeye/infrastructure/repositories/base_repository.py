# coding=utf-8
"""
Base Repository
"""
import logging

from sqlalchemy import not_
from sqlalchemy.orm import lazyload
from sqlalchemy.exc import MultipleResultsFound, NoResultFound
from hawkeye.infrastructure.database import db_engine

from hawkeye.infrastructure.exception import DatabaseError, ValidationException

logger = logging.getLogger(__name__)


class BaseRepository(object):
    """
    Base repository
    """

    @staticmethod
    def session(tenant_id=None):
        """
        returns session
        :return:
        """
        return db_engine.get_session(tenant_id)

    def create(self, item):
        self.session().add(item)
        self.session().flush()
        return item

    def create_all(self, items):
        """
        creates multiple items from list
        :param items:
        :return:
        """
        self.session().add_all(items)
        self.session().flush()
        return items

    def update(self, item):
        try:
            self.session().add(item)
            self.session().flush()
            return item
        except Exception as exp:
            raise DatabaseError(exp.__str__())

    def _save(self, item):
        try:
            self.session().add(item)
            self.session().flush()
        except Exception as exp:
            logger.exception("DatabaseError")
            raise DatabaseError(exp.__str__())
        return item

    def _update(self, item):
        try:
            self.session().merge(item)
            self.session().flush()
        except Exception as exp:
            raise DatabaseError(exp.__str__())
        return item

    def _update_all(self, items):
        for item in items:
            self.session().merge(item)
        self.session().flush()
        return items

    def dumb_save(self, item):
        try:
            self.session().add(item)
            self.session().flush()
        except Exception as exp:
            raise DatabaseError(exp.__str__())

    def _save_all(self, items):
        self.session().add_all(items)
        self.session().flush()
        return items

    def _bulk_save_or_update(self, items):
        try:
            self.session().bulk_save_objects(items, preserve_order=False)
        except Exception as exp:
            DatabaseError(exp.__str__())

    def _bulk_update_mappings(self, model, mapping_dicts):
        if not mapping_dicts:
            return
        try:
            self.session().bulk_update_mappings(model, mapping_dicts)
        except Exception as exp:
            DatabaseError(exp.__str__())

    def filter(self, model, *queries, for_update=False, nowait=True):
        """
        :param model:
        :param queries:
        :param for_update:
        :param nowait:
        :return:
        """
        queryset = self.session().query(model)
        queryset = queryset.filter(*queries)
        if for_update:
            queryset = queryset.with_for_update(nowait=nowait)
        return queryset

    def exclude(self, query, exclude_queries):
        real_excludes = [not_(ex) for ex in exclude_queries]
        return query.filter(*real_excludes)

    def filter_by_join(self, models, join_clause, *queries):
        """
        :param models:
        :param join_clause:
        :param queries:
        :return:
        """
        queryset = self.session().query(*models).filter(join_clause)
        items = queryset.filter(*queries)
        return items

    def get(self, model, **queries):
        """
        :param model:
        :param queries:
        :return:
        """
        queryset = self.session().query(model)

        for attr, value in queries.items():
            if value:
                value = "%s" % value
            queryset = queryset.filter(getattr(model, attr) == value)
        try:
            return queryset.one()
        except NoResultFound:
            return None
        except MultipleResultsFound:
            message = "Multiple objects %s found" % model
            raise ValidationException(description=message)
        except Exception as exp:
            raise DatabaseError(exp.__str__())

    def get_for_update(self, model, nowait=True, **queries):
        """
        The query will lock the row that is returned.
        If the transaction cannot lock the row (which will happen when some other transactions have obtained the lock),
        then:
            - If `nowait=True`, the query will fail with error
            - If `nowait=False`, the query will wait for the lock to get released.
        :param model:
        :param nowait:
        :param queries:
        :return:
        """
        queryset = self.session().query(model)
        for attr, value in queries.items():
            if value:
                value = "%s" % value
            queryset = queryset.filter(getattr(model, attr) == value)

        # Forcing lazy load here because
        # https://www.postgresql.org/message-id/<EMAIL>
        queryset = queryset.options(lazyload("*"))
        try:
            return queryset.with_for_update(nowait=nowait).one()
        except MultipleResultsFound:
            message = "Multiple objects %s found" % model
            raise ValidationException(description=message)
        except Exception as exp:
            raise DatabaseError(exp.__str__())

    def mark_deleted(self, model, filter_queries, exclude=None):
        queryset = self.filter(model, *filter_queries)
        if exclude:
            exclude_queries = []
            for item in exclude:
                exclude_queries.append(~item)
            queryset = queryset.filter(*exclude_queries)
        queryset.update({"deleted": True}, synchronize_session=False)

    def delete(self, item):
        self.session().delete(item)
        self.session().flush()

    def rollback_transaction(self):
        try:
            self.session().close()
        except Exception as exp:
            raise DatabaseError(exp.__str__())

    def query(self, model):
        return self.session().query(model)

    def persist(self, unsaved_object):
        self.session().add(unsaved_object)
        self.session().flush()

        return unsaved_object

    def persist_all(self, model_object_list):
        self.session().add_all(model_object_list)
        self.session().flush()

        return model_object_list
