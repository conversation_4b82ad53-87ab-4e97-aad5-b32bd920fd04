from typing import List

from hawkeye.domain.entities.price_update_config import PriceUpdateConfig
from hawkeye.infrastructure.database.models import PriceUpdateConfigModel
from hawkeye.infrastructure.exception import DatabaseError
from hawkeye.infrastructure.repositories.adaptors.price_update_config_adaptor import PriceUpdateConfigAdaptor
from hawkeye.infrastructure.repositories.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class PriceUpdateConfigRepository(BaseRepository):
    _model = PriceUpdateConfigModel
    _adaptor = PriceUpdateConfigAdaptor()

    def load_all(self):
        price_update_config_models = self.query(self._model).filter(self._model.is_active==True).all()
        return [self._adaptor.to_domain_entity(price_update_config_model) for price_update_config_model in price_update_config_models]

    def load_all_for_current_time(self, hour, minute):
        price_update_config_models = self.query(self._model).filter(self._model.is_active==True, self._model.hour==hour, self._model.minute==minute).all()
        return [self._adaptor.to_domain_entity(price_update_config_model) for price_update_config_model in price_update_config_models]
