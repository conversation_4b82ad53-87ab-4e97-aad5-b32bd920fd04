from hawkeye.constants.hawkeye_constant import RuleType
from hawkeye.domain.value_objects.rule_config_detail import RuleConfigDetail
from hawkeye.domain.value_objects.rule_definition import RuleDefinition
from hawkeye.infrastructure.base_entity import EntityChangeTracker


class BaseRoomTypePriceRule(EntityChangeTracker):
    """
    Base Room Type Price Rule
    """

    def __init__(
        self,
        hotel_id,
        rule_name,
        rule_type,
        config: RuleConfigDetail,
        is_input_price,
        factor,
        sum_factor,
        priority,
        start_price,
        end_price,
        is_deleted,
        rule_id=None,
        created_at=None,
        new=True,
        dirty=True,
        file_id=None,
    ):
        super().__init__(new, dirty)
        self.rule_id = rule_id
        self.hotel_id = hotel_id
        self.rule_name = rule_name
        self.rule_type = rule_type
        self.config = config
        self.is_input_price = is_input_price
        self.factor = factor
        self.sum_factor = sum_factor
        self.priority = priority
        self.start_price = start_price
        self.end_price = end_price
        self.created_at = created_at
        self.is_deleted = is_deleted
        self.file_id = file_id

    def to_json(self):
        return RuleDefinition(
            RuleType(self.rule_type),
            self.config,
            self.is_input_price,
            self.start_price,
            self.factor,
            self.sum_factor
        ).to_json()

    def to_dict(self):
        return dict(
            hotel_id=self.hotel_id,
            rule_name=self.rule_name,
            rule_type=self.rule_type,
            config=self.config.to_json() if self.config else None,
            is_input_price=self.is_input_price,
            factor=self.factor,
            sum_factor=self.sum_factor,
            priority=self.priority,
            start_price=self.start_price,
            end_price=self.end_price,
            is_deleted=self.is_deleted,
            rule_id=self.rule_id,
            created_at=self.created_at,
            file_id=self.file_id
        )

    @staticmethod
    def from_json(price_rule):
        rule_type = RuleType(price_rule["rule_type"])
        rule_config_detail_class = RuleDefinition.RULE_CONFIG_DETAIL_CLASS_MAP.get(
            rule_type
        )
        config = rule_config_detail_class.from_json(
            rule_detail_json=price_rule["config"]
        ) if rule_config_detail_class else None
        return BaseRoomTypePriceRule(
            hotel_id=price_rule["hotel_id"],
            rule_name=price_rule["rule_name"],
            rule_type=price_rule["rule_type"],
            config=config,
            is_input_price=price_rule["is_input_price"],
            factor=float(price_rule["factor"]) if price_rule["factor"] else price_rule["factor"],
            sum_factor=float(price_rule["sum_factor"]) if price_rule["sum_factor"] else price_rule["sum_factor"],
            priority=price_rule["priority"],
            start_price=float(price_rule["start_price"]) if price_rule["start_price"] else price_rule["start_price"],
            end_price=float(price_rule["end_price"]) if price_rule["end_price"] else price_rule["end_price"],
            is_deleted=price_rule["is_deleted"],
            rule_id=price_rule["rule_id"],
            created_at=price_rule["created_at"],
            file_id=price_rule.get("file_id"),
        )

    def update_pricing_rule_details(self, update_price_rule_dto):
        self.config = update_price_rule_dto.config
        self.factor = update_price_rule_dto.factor
        self.sum_factor = update_price_rule_dto.sum_factor
        self.start_price = update_price_rule_dto.start_price
        self.end_price = update_price_rule_dto.end_price

    def mark_delete(self):
        if self.is_deleted:
            return
        self.is_deleted = True
        self.mark_dirty()
