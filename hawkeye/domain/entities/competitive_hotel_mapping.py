from hawkeye.infrastructure.base_entity import EntityChangeTracker


class CompetitiveHotelMapping(EntityChangeTracker):
    def __init__(
        self,
        internal_hotel_id,
        competitive_hotel_id,
        is_active=False,
        normalization_factor=1.0,
        id=None,
        new=True,
        dirty=False,
        file_id=None,
    ):
        super().__init__(new, dirty)
        self.id = id
        self.internal_hotel_id = internal_hotel_id
        self.competitive_hotel_id = competitive_hotel_id
        self.is_active = is_active
        self.normalization_factor = normalization_factor
        self.file_id = file_id

    def mark_inactive(self):
        self.is_active = False
        self.mark_dirty()

    def update_normalization_factor(self, new_factor):
        if self.normalization_factor != new_factor:
            self.normalization_factor = new_factor
            self.mark_dirty()

    def mark_dirty(self):
        self.dirty = True
