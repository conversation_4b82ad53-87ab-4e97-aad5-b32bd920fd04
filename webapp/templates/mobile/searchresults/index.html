{% extends "root-mobile.html" %}{% load url_tag %}
{% load staticfiles %} {% load url_hashifier %}
{% block header %}

{% include "mobile/includes/header.html" %}

{% endblock %}
{% block content %}
<div class="result-page">
    <div class="result-page__search-container result-search">
        <span class="result-search__icon">
            <i class="icon-search"></i>
        </span>
        <div class="result-search__body">
            <h2 class="result-page__title">
                {% if queryParams.locality %}<span>{{queryParams.locality}}, </span>{% endif %}
                <span>{{queryParams.search}}</span>
            </h2>
            <div class="result-search__text">
                <span>{{queryParams.checkIn}}</span> -
                <span>{{queryParams.checkOut}}</span>
            </div>
            <div class="result-search__text">
                <span>{{queryParams.no_of_nights}} Night{{queryParams.no_of_nights|pluralize}}</span>
                <strong> &#183; </strong>
                <span>{{queryParams.rooms}} Room{{queryParams.rooms|pluralize}}</span>,
                <span>{{queryParams.guests}} Guest{{queryParams.guests|pluralize}}</span>
            </div>
        </div>
        <button id="modifySearch" class="btn--round v-center result-search__modify"
                data-toggle="modal" data-target="#searchModal">Modify</button>

    </div>

    <div class="result-page__content">
        <div class="result-page__filter-container clearfix">
            <div count="{{ results|length }}" class="lfloat analytics-count result-page__count container"><span id="filteredResultCount">{{ results|length }}</span> of {{ results|length }} results</div>
            <div class="filter-container rfloat" id="resultFilters"></div>
        </div>
        <input type="hidden" id="hiddenFilterData" name="name" value="{{filterData}}">
        <div class="results">
            {% if results|length < 1 or isNearby == True %}
                <div class="no-result">
                    <div class="no-result__container">
                        <div class="no-result__icon">
                            <i class="sprite-illustration sprite-illustration--empty"></i>
                        </div>
                        <div class="no-result__text">
                            <h3 class="no-result__title">Uh - oh!</h3>
                            <p class="no-result__sub">
                                we still do not have any results that matches your search, but we might soon :).
                                <br> Meanwhile checkout these cool places nearby.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif%}
            <div class="search-results-container">
                <div id="searchResults">

                {# include "mobile/searchresults/hotel.html" with results=results #}
                {% for hotel in results%}
                <div soldout="{{hotel.available}}" style="order:{{forloop.counter0}}" class="results__row hotelAvailibility-{{hotel.available}}"
                     data-category = "{{hotel.categories|join:'||'}}" data-locality="{{hotel.locality}}"
                     data-available="{{hotel.available}}" data-price="{{hotel.price|floatformat:"0"}}" data-id="{{hotel.id}}"
                     data-distance="{{hotel.distance|floatformat:"2"}}" data-recommend="{{hotel.priority|floatformat:"0"}}">
                    <a tags="{{hotel.categories|join:','}}" class="analytics-hotelname js-hotel-link hotel__name__link" href="{{hotel.hotelUrl}}" hotelname="{{hotel.hotelName}}">
                        <input type="hidden" class="js-hidden-amenities" name="name" value="{{hotel.amenities|join:'||'}}">
                        <div class="results__item hotel">

                            <div class="hotel__content">
                                {% if hotel.promoTagline %}
                                <div promocode="{{hotel.promoTagline}}" class="a-promo hotel__promo"><i class="icon-deals"></i>{{hotel.promoTagline}}</div>
                                {% endif %}


                                    <div class="hotel__details">
                                        <div class="hotel__images">
                                            <div class="hotel__img-cont" style="background-image: url('{% get_static_url %}{{ 'mobile/images/treeboleaf.png'|hashify:'mobile' }}');"><img class="" src="{{ hotel.image.imageUrl }}?w=75&fit=crop&fm=jpg&h=75"></div>
                                        </div>
                                        <div class="hotel__name">
                                            {{ hotel.hotelName }}<icon class="icon-right rfloat"></icon>
                                            {% for categoty in hotel.categories %}
                                            <span class="category-tag">{{categoty}}</span>
                                            {% endfor%}
                                        </div>
                                        <div class="hotel__info">
                                            <div locality="{{hotel.area}}" city="{{hotel.city}}" class="hotel__address analytics-address">
                                                <div  class="street  pos-rel">{{ hotel.locality }}, {{ hotel.city }}</div>
                                                {% if isLocalitySearched %}
                                                <div class="hotel__from">
                                                    {{hotel.distance}} km from {{localityName}}
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="hotel__price">
                                                {% if hotel.basePrice != hotel.price and hotel.basePrice > hotel.price %}
                                                <div class="hotel__price--best"><span class="line-through">&#8377;{{hotel.basePrice|floatformat:"0"}}</span></div>
                                                {% endif %}
                                                <div class="a-price hotel__price--available" price='{{hotel.price|floatformat:"0"}}'><span class="hotel__price__label">Incl of tax</span>&#8377;<strong>{{hotel.price|floatformat:"0"}}</strong></div>
                                                <!-- <div class="hotel__price__breakup hover-popup">
                                                    <div class="title anchor">Price break-up</div>
                                                    <div class="hotel__price__breakup__popup hover-popup__body hover-popup__twisty hover-popup__twisty--left">
                                                        <div class="breakup__item flex-row flex--space-between">
                                                            <div class="breakup__label">Room Price</div>
                                                            <div>&#8377;{{hotel.priceBreakup.price|floatformat:"0"}}</div>
                                                        </div>
                                                        <div class="breakup__item flex-row flex--space-between">
                                                            <div class="breakup__label">Taxes</div>
                                                            <div>&#8377;{{hotel.priceBreakup.tax|floatformat:"0"}}</div>
                                                        </div>
                                                        <div class="breakup__item flex-row flex--space-between">
                                                            <div class="breakup__label">Discount</div>
                                                            <div>- &#8377;{{hotel.priceBreakup.discount|floatformat:"0"}}</div>
                                                        </div>
                                                        <div class="breakup__item breakup__item--total flex-row flex--space-between flex--align-center">
                                                            <div class="breakup__label">Total</div>
                                                            <div class=""><span class="total-price">&#8377;{{hotel.priceBreakup.total|floatformat:"0"}}</span> <br /><span class="inc">Incl all taxes</span></div>
                                                        </div>

                                                        <div class="text-center breakup__per-night-price anchor" id="price-detail-search">view daily price breakup</div>
                                                    </div>
                                                </div> -->
                                            </div>
                                        </div>

                                    </div>

                                <div class="hotel__footer">
                                    <div class="hotel__action">
                                        {% if hotel.roomsLeft < 3 and hotel.roomsLeft > 0 %}
                                        <div class="lfloat hotel__price__label v-center">
                                            {{hotel.roomsLeft}} room{{hotel.roomsLeft|pluralize}} left
                                        </div>
                                        {% endif %}
                                        {% if hotel.available%}
                                        <object>
                                            <a href="{{hotel.quickbookUrl }}" class="analytics-quickbook js-hotel-quickbook hotel__action__book btn btn--round">QUICK BOOK</a>
                                        </object>
                                        {% else %}
                                        <button class="hotel__action__book btn btn--secondary btn--round">SOLD OUT</button>
                                        {% endif%}

                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                    <input class="js-pricedetailsdata" type="hidden" value='{{hotel.nights}}'/>
                </div>
                {% endfor%}
                {# include "mobile/searchresults/hotel.html" with results=results #}

                </div>
                {% if marketBanner %}
                    {% if results|length > 0 %}
                    <div id="marketingBanner" class="result-page__banner" style="order:{{marketBanner.position}}">
                        <div class="result-page__banner__cont">
                            <a class="analytics-marketingbanner" href="{{marketBanner.targeturl}}">
                                <img src="{{marketBanner.image}}" class="lazy" alt="" /></a>
                        </div>
                    </div>
                    {% endif%}
                {% endif%}

                <div class="no-result__container hide" id="noFilterResult">
                    <div class="no-result__icon">
                        <i class="sprite-illustration sprite-illustration--empty"></i>
                    </div>
                    <div class="no-result__text">
                        <div class="no-result__title">NO results found matching your criteria!</div>
                        <div class="no-result__sub">We couldn't find any result that matched your criteria. Try applying different filters !
                        </div>
                    </div>
                </div>

                {% if cityData %}
                <div class="seo-city">
                    <h2 class="seo-city__title text-left">ABOUT {{cityName|upper}}</h2>
                    <p class="seo-city__content">{{cityData|safe}}</p>
                </div>
                {% endif %}
            </div>
        </div>

    </div>
</div>


{% endblock %}
