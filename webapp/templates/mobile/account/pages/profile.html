<div class="pages profile-page hide" id="profile">
	<div class="pages__heading">Edit Profile</div>
<!-- 	<div class="facebook flex-row">
		<div class="facebook__import-detail">
			<span class="sprite-illustration sprite-illustration--tip"></span><span class="pos-rel facebook__import-text">Want to save time? Import your profile details form facebook.</span>
		</div>
		<div class="facebook__import-button">
			<button class="btn btn--facebook facebook__button-modify fb_profile">
                <div class="btn__flex-wrapper"><i class="icon-fb"></i><span class="social-pipe">|</span><span>IMPORT</span></div>
            </button>
		</div>

	</div> -->

<!-- 	<div class="facebook__subheading">
			Fill in your details
	</div>
 -->
	<div>
		<form class="profile-detail-form">
			<div class="profile-detail-form__item">
					<label class="profile-detail-form__label">First name</label>
				    <input id="firstName" name="first_name" type="text" class="profile-detail-form__input" required="true" value=""/>

			</div>

			<div class="profile-detail-form__item">
					<label class="profile-detail-form__label">Last name</label>
				    <input id="lastName" name="last_name" type="text" class="profile-detail-form__input" value=""/>

			</div>

			<div class="profile-detail-form__item">
				<div>
					<label class="profile-detail-form__label">Gender</label>
				</div>
				<div class="select">
				    <select id="gender" name="gender" class="profile-detail-form__input-gender" value="">

						<option selected="selected" value="M">M</option>
						<option value="F">F</option>

					</select>
			    </div>
			</div>

			<div class="profile-detail-form__item flex-column">
				<div>
					<label class="profile-detail-form__label">Date of birth</label>
				</div>
			    <div class="flex-row">
					<div id="birthDate" name="dob" class="profile-detail-form__date search-box__calendar daterange daterange--single" value=""></div>
			    </div>

			</div>

			<div class="profile-detail-form__item flex-column">
				<div>
					<label class="profile-detail-form__label">Anniversary</label>
				</div>
			    <div class="flex-row">
			    	<div id="anniversaryDate" name="anniversary" class="profile-detail-form__date search-box__calendar daterange daterange--single" value=""></div>
			    </div>
			</div>

			<div class="profile-detail-form__item">
				<label class="profile-detail-form__label">Email</label>
			    <input id="email" name="email" type="email" class="profile-detail-form__input" required="true" value="" readonly/>

			</div>

			<div class="profile-detail-form__item">
				<label class="profile-detail-form__label">Phone Number</label>
		    	<input id="mobile" type="number" name="phone" class="profile-detail-form__input"  value=""
    			data-parsley-type="number"
            	data-parsley-trigger="change"
            	data-parsley-type-message="Enter valid mobile number"
            	data-parsley-required-message="Please enter valid mobile number"
            	data-parsley-minlength="10"
            	data-parsley-maxlength="10"/>
			</div>

			<div class="profile-detail-form__item">
					<label class="profile-detail-form__label">Where do you live?</label>
				    <input id="location" name="city" type="text" class="profile-detail-form__input"  value=""/>

			</div>

			<div class="profile-detail-form__item">
				<label class="profile-detail-form__label">Work email</label>
			    <input id="workEmail" name="work_email" type="text" class="profile-detail-form__input" required="true"value=""
		    	data-parsley-type="email"
            	data-parsley-trigger="change"
            	data-parsley-type-message="Please enter a valid email address"
            	data-parsley-required-message="Please enter a valid email address"/>

			</div>

			<div class="separator"></div>
			<div class="text-left">
				<input id="submitProfileDetail" type="submit" class="btn profile-detail-form__save-btn submit" value="save"/>
			</div>

		</form>
	</div>

</div>
