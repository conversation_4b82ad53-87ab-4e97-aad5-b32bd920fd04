{% extends "root-mobile.html" %} {% load url_tag %} {% block header %} {% include "mobile/includes/header.html" %} {% endblock %} {% block content %}
<div class="forgot-password-container auth-page">
    <div class="auth-page__header">Reset Password</div>
    <p class="msg">Enter the email address associated with your account, and we'll email you a link to reset your password. </p>
    <form id="forgotForm">
        <div class="forgot__error alert alert--error hide"></div>
        <div class="forgot__success alert alert--success hide"></div>
        <div class="float-labels">
            <input id = "forgotEmailInput" class="float-labels__input forgot__full" pattern=".+" type="email" value="" placeholder="Email" required="true" data-parsley-type="email" data-parsley-trigger="change" data-parsley-type-message="Enter valid email" data-parsley-required-message="Please enter a valid email address"
            />
            <label class="float-labels__label">Email</label>
         </div>
        <input type="submit" id="forgotButton" class="btn reset-button auth__button forgot__btn js-forgot-btn" value="SEND RESET LINK" />
    </form>
    <a href="/login/" class="auth__login-back-link"><i class="icon-back"></i> Back to login</a>

</div>
{% endblock %}
