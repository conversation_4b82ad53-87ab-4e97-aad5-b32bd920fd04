<div id="checkin-checkout-form" class="hide">
		<h4 class="contactus__subheading mb20">Tell us more about your requirement</h4>
					<form id="checkin-checkout">
					<div class="flex-row">	
						<div class="flex-column">

							<div class="form-labels">
										<label class="form-labels__label">Name of the Guest *</label>
									    <input id="name" name="guestname" type="text" class="form-labels__input form-labels__border" required="true"
								 		   	data-parsley-required="true"
			                     	       data-parsley-trigger="change" 
			                     	      data-parsley-type-message="Enter guest name" 
			                      	      data-parsley-required-message="Please enter guest name here"/>
								    
							</div>

							<div class ="flex-row">
								<div class="form-labels">
									<label class="form-labels__label">Hotel Name </label>
							 	   <input id="checkin" name="hotel" type="text" class="form-labels__input form-labels__border" />
								</div>
								
							</div>
							<div class ="flex-row">
								<div class="form-labels flex-column">
									<label class="form-labels__label">Check in Date *</label>
								    <input id="checkout" name="checkindate" type="date" class="form-labels__input form-labels__border"/>
								    
								</div>
							</div>

						</div>

						<div id="checkin-checkout-policy" class="in-out-form-policy text-left">
							<h3 class="in-out-form-policy__heading"> Check in / Check out Policy: </h3>
							<ul type="1" class="form_policy__ul">
								<li class="form_policy__li-class">Standard check-in time: 12 PM | Standard check-out time: 11 AM</li>
								<li class="form_policy__li-class">Early check-in from 6 AM to 12 PM and late check-out from 11 AM to 3 PM is allowed free of charge subject to availability at the time of check-in or check-out.</li>
								<p>For more details, <a href="/faq/#checkin-checkout-policy" target="_blank">click here</a>.
							</ul>

						</div>
					</div>

						

							<div class="flex-row">
								<div class="form-labels mr20">
									
										<label class="form-labels__label">Contact Number *</label>
								    	<input id="mobile" name="mobile" type="number" class="form-labels__input form-labels__border"  required="true"
								    	data-parsley-type="number"
		                            	data-parsley-trigger="change" 
		                            	data-parsley-type-message="Enter valid mobile number" 
		                            	data-parsley-required-message="Please enter valid mobile number"
		                            	data-parsley-minlength="10"
		                            	data-parsley-maxlength="10"

		                            	/>
		                      	   
								    
								</div>

								<div class="form-labels">

									
										<label class="form-labels__label">Email *</label>
								   		<input id="email" name="email" type="email" class="form-labels__input form-labels__border" required="true" 
								    	data-parsley-type="email"
		                            	data-parsley-trigger="change" 
		                            	data-parsley-type-message="Enter valid email" 
		                            	data-parsley-required-message="Please enter a valid email address"
		                            	/>
		                        	
								    
								</div>
							</div>
						

						<div class="form-labels">
							<label class="form-labels__label">Details:</label>
						    <textarea id="comment" name="detail" rows="5" cols="65" class="form-labels__input-area form-labels__border"> </textarea>
						    
						</div>

						<div class="separator"></div>
						<div class="text-left">
							<input id="submit" type="submit" class="mt30 btn contactus__submit submit" value="submit"/>
						</div>
					</form>
	</hide>