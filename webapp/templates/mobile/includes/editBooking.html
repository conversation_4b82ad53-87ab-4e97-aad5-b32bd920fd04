{% load staticfiles %} {% load url_hashifier %}
<div class="search-box">
    <div class="modal-header pos-rel text-center">Edit Booking Details
        <div id="closeEdit" class="hand icon icon-close pos-abs">
            <i class="fa fa-times"></i>
        </div>
    </div>
    <div class="modal-body search-widget-body">
        <form action="/search/">
            <div class="row">
                <div class="col col-1 pos-rel">
                    <input type="text" name="checkin" id="editCheckIn" class="input-text checkin date" placeholder="Check in">
                    <i class="fa fa-calendar pos-abs"></i>
                </div>
                <div class="col col-2 pos-rel">
                    <input type="text" name="checkOut" id="editCheckOut" class="input-text checkout date" placeholder="Check Out">
                    <i class="fa fa-calendar pos-abs"></i>
                </div>
            </div>
            <div class="row">
                <div class="col col-1 guests select select-block select-large">
                    <div class="select select-block select-large">
                        <select id="editGuests" name="guests">
                            <option value="1">1 guest</option>
                            <option value="2">2 guests</option>
                            <option value="3">3 guests</option>
                            <option value="4">4 guests</option>
                            <option value="5">5 guests</option>
                            <option value="6">6 guests</option>
                            <option value="7">7 guests</option>
                            <option value="8">8 guests</option>
                            <option value="9">9 guests</option>
                            <option value="10">10 guests</option>
                            <option value="11">11 guests</option>
                            <option value="12">12 guests</option>
                            <option value="13">13 guests</option>
                            <option value="14">14 guests</option>
                            <option value="15">15 guests</option>
                            <option value="16">16 guests</option>
                        </select>
                    </div>
                </div>
                <div class="col col-2 rooms">
                    <div class="select select-block select-large">
                        <select id="editRooms" name="rooms">
                            <option value="1">1 room</option>
                            <option value="2">2 rooms</option>
                            <option value="3">3 rooms</option>
                            <option value="4">4 rooms</option>
                            <option value="5">5 rooms</option>
                            <option value="6">6 rooms</option>
                            <option value="7">7 rooms</option>
                            <option value="8">8 rooms</option>
                            <option value="9">9 rooms</option>
                            <option value="10">10 rooms</option>
                            <option value="11">11 rooms</option>
                            <option value="12">12 rooms</option>
                            <option value="13">13 rooms</option>
                            <option value="14">14 rooms</option>
                            <option value="15">15 rooms</option>
                            <option value="16">16 rooms</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="" id="">
                <div class="guest-text r-t-title">Room Type</div>
                {% for roomType in room_types %}
                <div class="radio-cont pos-rel">
                    <input type="radio" class="pos-abs js-roomtype-radio radio" name="room-type-radio" id="{{roomType.room_type}}" value="{{roomType.room_type}}">
                    <label for="{{roomType.room_type}}">{{roomType.room_type}}</label>
                </div>
                {% endfor %}
            </div>
            <div class="row">
                <button type="submit" id="editSubmitBtn" class="btn btn-primary analytics_search_a_treebo">
                    <!-- <i class="fa fa-search"></i> -->Check Availibility
                </button>
            </div>
            <div id="errorMsg" class="alert alert-error hide">jaina</div>
        </form>
    </div>
</div>
