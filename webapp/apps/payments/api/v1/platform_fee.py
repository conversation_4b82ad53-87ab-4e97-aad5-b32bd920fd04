from decimal import Decimal

from apps.payments.constants import DEFAULT_PLATFORM_FEE
from apps.payments.service.platform_fee_service import PlatformFeeService
from base import log_args
from base.renderers import TreeboCustomJSONRenderer
from base.views.api import TreeboAPIView
from common.custom_logger.booking.log import get_booking_logger
from rest_framework.response import Response
from apps.bookings.models import Booking as BookingModel
from rest_framework import status

logger = get_booking_logger(__name__)


class PlatformFee(TreeboAPIView):
    renderer_classes = [TreeboCustomJSONRenderer, ]
    authentication_classes = []

    @log_args(logger)
    def get(self, request):
        booking_id = request.GET.get('booking_id')
        if not booking_id:
            return Response({"error": "Booking ID not provided"},
                            status=status.HTTP_400_BAD_REQUEST)

        try:
            booking = BookingModel.objects.get(order_id=booking_id)
        except BookingModel.DoesNotExist:
            return Response({"error": "Booking not found"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"platform_fee": booking.platform_fee
                        or Decimal(DEFAULT_PLATFORM_FEE)},
                        status=status.HTTP_200_OK)


class PlatformFees(TreeboAPIView):
    renderer_classes = [TreeboCustomJSONRenderer, ]
    authentication_classes = []

    @log_args(logger)
    def get(self, request):
        booking_ids = request.GET.get('booking_ids')
        if not booking_ids:
            return Response({"error": "Booking IDs not provided"},
                            status=status.HTTP_400_BAD_REQUEST)
        booking_ids = booking_ids.split(',')
        try:
            response_data = []
            bookings = BookingModel.objects.filter(order_id__in=booking_ids)
            if bookings.exists():
                response_data = [
                    {"booking_id": booking.order_id,
                     "platform_fee": booking.platform_fee or Decimal(DEFAULT_PLATFORM_FEE)}
                    for booking in bookings
                ]

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(response_data, status=status.HTTP_200_OK)
