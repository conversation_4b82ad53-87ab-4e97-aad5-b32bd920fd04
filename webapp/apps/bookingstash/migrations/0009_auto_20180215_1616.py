# -*- coding: utf-8 -*-
# Generated by Django 1.9 on 2018-02-15 16:16


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bookingstash', '0008_auto_20170426_0842'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='reservationbooking',
            name='group_stay_source',
            field=models.CharField(default='sx', max_length=100),
        ),
        migrations.AlterField(
            model_name='reservationbooking',
            name='status',
            field=models.CharField(choices=[('RESERVE', 'Reserved'), ('CHECKIN', 'Checkin'), ('CHECKOUT', 'Checkout'), ('CANCEL', 'Cancelled')], default='RESERVE', max_length=100),
        ),
        migrations.AlterField(
            model_name='reservationbooking',
            name='stay_source',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.Alter<PERSON>ield(
            model_name='reservationbookingmessage',
            name='group_stay_source',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='reservationbookingmessage',
            name='message_status',
            field=models.CharField(choices=[('Modify', 'Modify'), ('Cancel', 'Cancel'), ('Commit', 'Commit')], default='Commit', max_length=200),
        ),
        migrations.AlterField(
            model_name='reservationbookingmessage',
            name='source',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='reservationbookingmessage',
            name='status',
            field=models.CharField(choices=[('RESERVE', 'Reserved'), ('CHECKIN', 'Checkin'), ('CHECKOUT', 'Checkout'), ('CANCEL', 'Cancelled')], default='RESERVE', max_length=200),
        ),
        migrations.AlterField(
            model_name='reservationbookingmessage',
            name='stay_source',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='reservationrestriction',
            name='restriction_status',
            field=models.CharField(choices=[('DNR', 'Reserved'), ('FREE', 'Released')], default='DNR', max_length=200),
        ),
        migrations.AlterField(
            model_name='reservationrestrictionmessage',
            name='message_status',
            field=models.CharField(choices=[('Modify', 'Modify'), ('Cancel', 'Cancel'), ('Commit', 'Commit')], default='Commit', max_length=200),
        ),
        migrations.AlterField(
            model_name='reservationrestrictionmessage',
            name='restriction_status',
            field=models.CharField(choices=[('DNR', 'Reserved'), ('FREE', 'Released')], default='DNR', max_length=200),
        ),
    ]
