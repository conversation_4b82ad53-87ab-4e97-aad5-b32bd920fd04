# -*- coding: utf-8 -*-
# Generated by Django 1.9 on 2018-02-15 14:40


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0009_auto_20170908_0701'),
    ]

    operations = [
        migrations.AlterField(
            model_name='hoteltaoverallratings',
            name='hotel_ta_mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TAHotelIDMapping'),
        ),
        migrations.AlterField(
            model_name='reviewappconfiguration',
            name='award_description',
            field=models.TextField(default='TripAdvisor Certificate of Excellence - Awarded to properties that consistently earn great reviews'),
        ),
        migrations.AlterField(
            model_name='reviewappconfiguration',
            name='disable_for_hotel_list',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='reviewappconfiguration',
            name='enable_for_hotel_list',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='hotel_ta_mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TAHotelIDMapping'),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='location_api_url',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='location_api_url_response_json',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='location_api_url_response_status',
            field=models.CharField(choices=[('SUCCESS', 'SUCCESS'), ('FAILURE', 'FAILURE')], default='SUCCESS', max_length=100),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='location_reviews_api_url',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='location_reviews_api_url_response_json',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='location_reviews_api_url_response_status',
            field=models.CharField(choices=[('SUCCESS', 'SUCCESS'), ('FAILURE', 'FAILURE')], default='SUCCESS', max_length=100),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='user_location_reviews_api_url',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='user_location_reviews_api_url_response_json',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AlterField(
            model_name='taapiaudittable',
            name='user_location_reviews_api_url_response_status',
            field=models.CharField(choices=[('SUCCESS', 'SUCCESS'), ('FAILURE', 'FAILURE')], default='SUCCESS', max_length=100),
        ),
        migrations.AlterField(
            model_name='taawardshotelmapping',
            name='award',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TAAwardslist'),
        ),
        migrations.AlterField(
            model_name='taawardshotelmapping',
            name='hotel_ta_mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TAHotelIDMapping'),
        ),
        migrations.AlterField(
            model_name='tahotelidmapping',
            name='hotel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dbcommon.Hotel'),
        ),
        migrations.AlterField(
            model_name='tahotelreviews',
            name='hotel_ta_mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TAHotelIDMapping'),
        ),
        migrations.AlterField(
            model_name='tahotelsubratingmapping',
            name='hotel_ta_mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TAHotelIDMapping'),
        ),
        migrations.AlterField(
            model_name='tahotelsubratingmapping',
            name='subrating',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TASubrating'),
        ),
        migrations.AlterField(
            model_name='tareviewratingcountmapping',
            name='hotel_ta_mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reviews.TAHotelIDMapping'),
        ),
        migrations.AlterField(
            model_name='tareviewratingcountmapping',
            name='review_rating',
            field=models.CharField(choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5')], default='5', max_length=100),
        ),
    ]
