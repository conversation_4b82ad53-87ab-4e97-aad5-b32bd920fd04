import logging

from django.conf import settings

from base.views.template import TreeboTemplateView
from services.restclient.contentservicerestclient import ContentServiceClient

__author__ = 'devang'

logger = logging.getLogger(__name__)


class Join(TreeboTemplateView):
    template_name = "join/index.html"
    redirect_mobile_to_desktop_view = True

    def getData(self, request, args, kwargs):
        seo = ContentServiceClient.getValueForKey(request, 'seo_pages', 1)
        return {'scripts': ['desktop/js/joinus.js'],
                'styles': ['desktop/css/joinus.css'],
                'seo': seo['join'], 'footerCopyrightDisabled': True}

    def get_custom_response_headers(self):
        custom_headers = {}
        if settings.ENVIRONMENT.upper() in ['STAGING', 'DEV', 'PREPROD']:
            custom_headers['X-Robots-Tag'] = 'noindex'
        return custom_headers
