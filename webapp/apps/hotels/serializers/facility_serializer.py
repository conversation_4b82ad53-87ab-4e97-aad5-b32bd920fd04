from rest_framework import serializers

from dbcommon.models.facilities import Facility, FacilityCategory

__author__ = 'Rajdeep'


class FacilityCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = FacilityCategory
        fields = ('name',)


class FacilitySerializer(serializers.ModelSerializer):
    categories = FacilityCategorySerializer(many=True)

    class Meta:
        model = Facility
        fields = ('name', 'url', 'css_class', 'catalog_mapped_name', 'categories')
