import urllib.parse

from rest_framework import serializers

from dbcommon.models.location import City, PopularCity
from common.constants import common_constants as const

__author__ = 'Rajdeep'


class CitySerializer(serializers.ModelSerializer):
    image_url = serializers.CharField(source='cover_image')

    class Meta:
        model = City
        fields = (
            'id',
            'name',
            'description',
            'tagline',
            'subscript',
            'status',
            'portrait_image',
            'landscape_image',
            'image_url',
            'city_latitude',
            'city_longitude',
            'slug')


class PopularCitySerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    image_url = serializers.SerializerMethodField()
    hotel_count = serializers.SerializerMethodField()
    slug = serializers.SerializerMethodField()

    class Meta:
        model = PopularCity
        fields = ('id', 'call_out', 'name', 'slug', 'hotel_count', 'image_url')

    def get_name(self, obj):
        return obj.city.name

    def get_image_url(self, obj):
        url = str(obj.city.popular_city_thumbnail)
        return urllib.parse.unquote(url)

    def get_hotel_count(self, obj):
        return obj.city.hotel_set.filter(status=const.ENABLED).count()

    def get_slug(self, obj):
        return obj.city.slug

