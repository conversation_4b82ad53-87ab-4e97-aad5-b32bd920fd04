import logging

from rest_framework.response import Response

from apps.hotels.serializers.city_serializer import PopularCitySerializer
from base.views.api import TreeboAPIView
from data_services.respositories_factory import RepositoriesFactory

logger = logging.getLogger(__name__)

city_data_service = RepositoriesFactory.get_city_repository()


class PopularCities(TreeboAPIView):
    def get(self, request):
        try:
            popular_city_list = city_data_service.get_popular_cities_from_db()
            popular_cities = PopularCitySerializer(popular_city_list, many=True).data
        except Exception as exc:
            logger.exception(exc)

        return Response(popular_cities)



