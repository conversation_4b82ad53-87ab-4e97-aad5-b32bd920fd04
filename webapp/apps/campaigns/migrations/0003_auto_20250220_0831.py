# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2025-02-20 08:31
from __future__ import unicode_literals

import apps.campaigns.models
import common.custom_model_fields.image_and_svg_field
import common.storages.custom_storages
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0002_auto_20250131_1345'),
    ]

    operations = [
        migrations.AddField(
            model_name='campaignbanner',
            name='attach_coupon_code',
            field=models.BooleanField(default=False, help_text='If true clicking on banner results in copying the Coupon code'),
        ),
        migrations.AddField(
            model_name='campaignbanner',
            name='coupon_code',
            field=models.CharField(blank=True, default=None, help_text='Coupon code associated with banner', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='campaignbanner',
            name='app_banner_detail_image',
            field=common.custom_model_fields.image_and_svg_field.ImageAndSvgField(
                blank=True,
                help_text='Image that will be shown on Campaign Details Page on app',
                null=True,
                storage=common.storages.custom_storages.CustomImageAWSStorage(),
                upload_to=apps.campaigns.models.custom_upload_path
            ),
        ),
        migrations.AlterField(
            model_name='campaignbanner',
            name='app_banner_image',
            field=common.custom_model_fields.image_and_svg_field.ImageAndSvgField(
                blank=True,
                help_text='Image that will be shown on Apps Pages',
                null=True,
                storage=common.storages.custom_storages.CustomImageAWSStorage(),
                upload_to=apps.campaigns.models.custom_upload_path
            ),
        ),
        migrations.AlterField(
            model_name='campaignbanner',
            name='desktop_banner_detail_image',
            field=common.custom_model_fields.image_and_svg_field.ImageAndSvgField(
                blank=True,
                help_text='Image that will be shown on Campaign Details Page on desktop',
                null=True,
                storage=common.storages.custom_storages.CustomImageAWSStorage(),
                upload_to=apps.campaigns.models.custom_upload_path
            ),
        ),
        migrations.AlterField(
            model_name='campaignbanner',
            name='desktop_banner_image',
            field=common.custom_model_fields.image_and_svg_field.ImageAndSvgField(
                blank=True,
                help_text='Image that will be shown on Website Pages',
                null=True,
                storage=common.storages.custom_storages.CustomImageAWSStorage(),
                upload_to=apps.campaigns.models.custom_upload_path
            ),
        ),
        migrations.AlterField(
            model_name='campaignbanner',
            name='msite_banner_detail_image',
            field=common.custom_model_fields.image_and_svg_field.ImageAndSvgField(
                blank=True,
                help_text='Image that will be shown on Campaign Details Page on mobile',
                null=True,
                storage=common.storages.custom_storages.CustomImageAWSStorage(),
                upload_to=apps.campaigns.models.custom_upload_path
            ),
        ),
        migrations.AlterField(
            model_name='campaignbanner',
            name='msite_banner_image',
            field=common.custom_model_fields.image_and_svg_field.ImageAndSvgField(
                blank=True,
                help_text='Image that will be shown on Mobile Pages',
                null=True,
                storage=common.storages.custom_storages.CustomImageAWSStorage(),
                upload_to=apps.campaigns.models.custom_upload_path
            ),
        ),
    ]
