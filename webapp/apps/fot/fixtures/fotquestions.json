[{"fields": {"section": "PROBLEMSOLVING", "question": "If you are staying at a hotel on a vacation with family, what would you do if you find rats in a room you just checked in?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 1}, {"fields": {"section": "PROBLEMSOLVING", "question": "What would you do if you win a million Rupees in a lottery?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 2}, {"fields": {"section": "PROBLEMSOLVING", "question": "How many differences can you see in the picture below?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 3}, {"fields": {"section": "PROBLEMSOLVING", "question": "You are on a beach in Goa. Your friend/spouse wants to go deep in the water but the there are some signs on the beach that say \"Do not go into the water due to high tides\". What would you do?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 4}, {"fields": {"section": "PROBLEMSOLVING", "question": "Its late night, your best friend is waiting for you to cut his/her birthday cake and you are stuck at a 3 minute long traffic signal with no Traffic at all. What would you do in this situation?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 5}, {"fields": {"section": "PROBLEMSOLVING", "question": "It’s Friday evening, and you are about to leave your office for a party with friends. But your Boss just turns up and requests you to stay for 2 more hours to complete an urgent assignment. You had planned your party long back, and your friends are waiting for you. How would you handle the situation?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 6}, {"fields": {"section": "PROBLEMSOLVING", "question": "One of your colleagues at the workplace asks you to lend him Rs.20,000 for a month for some personal emergency. You don’t know him well enough and are not comfortable lending money to him. What would you do?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 7}, {"fields": {"section": "ABOUTFOT", "question": "What documents do you need to check-in into the hotel for conducting an audit?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 8}, {"fields": {"section": "ABOUTFOT", "question": "After completing your free audit stay, what is the maximum allowed time to submit the feedback?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 9}, {"fields": {"section": "ABOUTFOT", "question": "Please Describe your best travel experience till date?", "question_type": "TEXTAREA"}, "model": "fot.fotsignupquestions", "pk": 10}, {"fields": {"section": "ABOUTFOT", "question": "Tell us something interesting about yourself. (What do you do, your hobbies, your special talents, any weird stories, or anything interesting about your life).", "question_type": "TEXTAREA"}, "model": "fot.fotsignupquestions", "pk": 11}, {"fields": {"section": "ABOUTFOT", "question": "Explain in at-least 50 words why do you want to be part of this program.", "question_type": "TEXTAREA"}, "model": "fot.fotsignupquestions", "pk": 12}, {"fields": {"section": "ABOUTFOT", "question": "Please tell us how did you get to know about the program?", "question_type": "TEXTAREA"}, "model": "fot.fotsignupquestions", "pk": 13}, {"fields": {"section": "ABOUTFOT", "question": "If you were referred by a friend, please mention his/her name. (We may surprise your friend with some cool rewards)", "question_type": "TEXTAREA"}, "model": "fot.fotsignupquestions", "pk": 14}, {"fields": {"section": "PERSONAL", "question": "Current Address", "question_type": "TEXT"}, "model": "fot.fotsignupquestions", "pk": 15}, {"fields": {"section": "PERSONAL", "question": "Resident city", "question_type": "DROPDOWN"}, "model": "fot.fotsignupquestions", "pk": 16}, {"fields": {"section": "PERSONAL", "question": "Marital Status", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 17}, {"fields": {"section": "PERSONAL", "question": "Social profile", "question_type": "TEXT"}, "model": "fot.fotsignupquestions", "pk": 18}, {"fields": {"section": "PERSONAL", "question": "Select the assets you own", "question_type": "CHECKBOX"}, "model": "fot.fotsignupquestions", "pk": 19}, {"fields": {"section": "PERSONAL", "question": "Which of the following best describes your occupation?\n", "question_type": "DROPDOWN"}, "model": "fot.fotsignupquestions", "pk": 20}, {"fields": {"section": "PERSONAL", "question": "How many audits are you likely to conduct in a month?", "question_type": "RADIO"}, "model": "fot.fotsignupquestions", "pk": 21}]