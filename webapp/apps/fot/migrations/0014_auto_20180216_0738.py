# -*- coding: utf-8 -*-
# Generated by Django 1.9 on 2018-02-16 07:38


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fot', '0013_auto_20180216_0736'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fot',
            name='status',
            field=models.CharField(
                choices=[
                    ('BLOCKED',
                     'BLOCKED'),
                    ('IN_PROGRESS',
                     'IN_PROGRESS'),
                    ('PASSED',
                     'PASSED'),
                    ('FAILED',
                     'FAILED'),
                    ('NOT_CONFIRMED',
                     'NOT_CONFIRMED')],
                default='IN_PROGRESS',
                max_length=100),
        ),
        migrations.AlterField(
            model_name='fotreservationsrequest',
            name='status',
            field=models.Char<PERSON>ield(
                choices=[
                    ('CHECKEDIN',
                     'CHECKEDIN'),
                    ('NOSHOW',
                     'NOSHOW'),
                    ('CANCEL_PENDING',
                     'CANCEL_PENDING'),
                    ('FEEDBACK_NOT_SUBMITTED',
                     'FEEDBACK_NOT_SUBMITTED'),
                    ('CANCELLED',
                     'CANCELLED'),
                    ('SCHEDULED',
                     'SCHEDULED'),
                    ('FEEDBACK_SUBMITTED',
                     'FEEDBACK_SUBMITTED'),
                    ('PENDING',
                     'PENDING'),
                    ('CHECKEDOUT',
                     'CHECKEDOUT')],
                default='PENDING',
                max_length=100),
        ),
        migrations.AlterField(
            model_name='fotsignupquestions',
            name='question_type',
            field=models.CharField(
                choices=[
                    ('RADIO',
                     'RADIO'),
                    ('TEXTAREA',
                     'TEXTAREA'),
                    ('TEXT',
                     'TEXT'),
                    ('DROPDOWN',
                     'DROPDOWN'),
                    ('CHECKBOX',
                     'CHECKBOX')],
                max_length=100),
        ),
    ]
