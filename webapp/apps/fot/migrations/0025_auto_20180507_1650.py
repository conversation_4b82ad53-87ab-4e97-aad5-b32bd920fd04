# -*- coding: utf-8 -*-
# Generated by Django 1.9 on 2018-05-07 16:50


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fot', '0024_auto_20180507_1642'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fot',
            name='status',
            field=models.CharField(choices=[('FAILED', 'FAILED'), ('BLOCKED', 'BLOCKED'), ('PASSED', 'PASSED'), ('NOT_CONFIRMED', 'NOT_CONFIRMED'), ('IN_PROGRESS', 'IN_PROGRESS')], default='IN_PROGRESS', max_length=100),
        ),
        migrations.AlterField(
            model_name='fotreservationsrequest',
            name='status',
            field=models.Char<PERSON>ield(choices=[('CANCEL_PENDING', 'CANCEL_PENDING'), ('FEEDBACK_SUBMITTED', 'FEEDBACK_SUBMITTED'), ('CANCELLED', 'CANCELLED'), ('FEEDBACK_NOT_SUBMITTED', 'FEEDBACK_NOT_SUBMITTED'), ('CHECKEDOUT', 'CHECKEDOUT'), ('CHECKEDIN', 'CHECKEDIN'), ('SCHEDULED', 'SCHEDULED'), ('PENDING', 'PENDING'), ('NOSHOW', 'NOSHOW')], default='PENDING', max_length=100),
        ),
        migrations.AlterField(
            model_name='fotsignupquestions',
            name='question_type',
            field=models.CharField(choices=[('CHECKBOX', 'CHECKBOX'), ('RADIO', 'RADIO'), ('DROPDOWN', 'DROPDOWN'), ('TEXT', 'TEXT'), ('TEXTAREA', 'TEXTAREA')], max_length=100),
        ),
        migrations.AlterField(
            model_name='fotsignupquestions',
            name='section',
            field=models.CharField(choices=[('ABOUT_FOT', 'ABOUT_FOT'), ('PROBLEM_SOLVING', 'PROBLEM_SOLVING'), ('PERSONAL', 'PERSONAL')], default='PERSONAL', max_length=100),
        ),
    ]
