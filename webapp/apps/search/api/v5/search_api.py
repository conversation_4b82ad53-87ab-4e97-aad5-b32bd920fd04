from datetime import datetime
import logging
from decimal import Decimal

from django.conf import settings
from django.core.cache import cache
from rest_framework import status
from rest_framework.response import Response

from apps.bookingstash.service.availability_service import ITSAvailabilityService
from apps.content.services.content_store_service import ContentStoreService
from apps.hotels.constants import CATALOG_MAPPED_BED_TYPES
from apps.hotels.service.hotel_image_service import HotelImageService
from apps.hotels.service.hotel_poi_service import HotelPOIService
from apps.search.constants import FacilityCategory, HotelCategory, \
    POPULAR_CATEGORY_COLLECTION, EXCLUDE_AMENITIES, HotelPolicyType
from apps.common.api_response import APIResponse
from apps.common.exceptions.custom_exception import InvalidBookingDates, NoHotelsFoundException
from apps.common.slack_alert import SlackAlertService
from apps.hotels.repositories.hotels_repository import HotelsRepository
from apps.hotels.service.hotel_policy_service import HotelPolicyService
from apps.hotels.service.hotel_facility_service import HotelFacilityService
from apps.hotels.service.hotel_landmark_service import HotelLandmarkService
from apps.hotels.service.hotel_service import HotelService
from apps.hotels.service.hotel_sub_brand_service import HotelSubBrandService
from apps.pricing.services.pricing_sorting_service import PricingSortingService
from apps.search.constants import DIRECT, GDC, DEFAULT_SEARCH_CHANNEL, FEATURE_TOGGLE_NAMES
from apps.search.dto.hotel_serarch_dto import HotelSearchQuery
from apps.search.serializers import SearchSerializer
from apps.search.services.categories.categories_services import CategoriesServices
from apps.search.services.search_v2 import SearchService
from apps.search.services.sku_search_cache_service import SkuSearchCacheService
from apps.seo.constants import NEARBY_DISTANCE_CAP
from base import log_args
from base.decorator.request_validator import validate_request
from base.decorators import timed
from base.views.api import TreeboAPIView
from common.constants.common_constants import ApplicationId
from common.services.feature_toggle_api import FeatureToggleAPI
from conf.gdc import TREEBO_BASE_HOST_URL as GDC_BASE_HOST_URL
from data_services.respositories_factory import RepositoriesFactory

logger = logging.getLogger(__name__)


class SearchResult(object):
    def __init__(self, hotels):
        self.hotels = hotels
        self.hotel_ids = []
        self.hotel_web_cs_id_dict = {}
        self.hotel_cs_ids = []
        for hotel in hotels:
            self.hotel_ids.append(hotel.id)
            self.hotel_web_cs_id_dict[hotel.id] = hotel.cs_id
            self.hotel_cs_ids.append(hotel.cs_id)

    def filter_hotel(self, hotel_id):
        for hotel in self.hotels:
            if hotel.id == hotel_id:
                return hotel


class SearchApiV5(TreeboAPIView):
    CACHE_TIMEOUT = 24 * 60 * 60  # seconds (1 day)
    GDC_CACHE_KEY = 'apicache_searchpage_v5_gdc_0_jank_{0}'
    DIRECT_CACHE_KEY = 'apicache_searchpage_v5_0_jank_{0}'
    AVAILABLE_ROOM_TYPES_CACHE_KEY = 'available_room_types_hotel_id_'
    AVAILABLE_ROOM_TYPES_CACHE_TIMEOUT = 2 * 60 * 60  # seconds (2 hours)
    validationSerializer = SearchSerializer
    search_landmark_repository = RepositoriesFactory.get_landmark_repository()

    @staticmethod
    def invalidate_cache():
        cache.delete_pattern(SearchApiV5.DIRECT_CACHE_KEY.format('*'))
        cache.delete_pattern(SearchApiV5.GDC_CACHE_KEY.format('*'))
        cache.delete_pattern(SearchApiV5.AVAILABLE_ROOM_TYPES_CACHE_KEY.format('*'))

    @staticmethod
    def get_cache_key(request_param, search_dto, application_id):
        key = "hotel_id_{0}_locality_name_{1}_city_name_{2}_state_name_{3}_country_name_{4}_" \
              "location_type_{5}_nearby_{6}_landmark_name_{7}_distance_cap_{8}_category_{9}_" \
              "amenity_{10}_meta_key_{11}_latitude_{12}_longitude_{13}_radius_{14}_checkin_{15}_" \
              "checkout_{16}_room_config_{17}_meta_hotel_id_{18}" \
            .format(request_param.get('hotel_id', ''),
                    request_param.get('locality', ''),
                    request_param.get('city', ''),
                    request_param.get('state', ''),
                    request_param.get('country', ''),
                    request_param.get('location_type', ''),
                    request_param.get('nearby', False),
                    request_param.get('landmark', ''),
                    request_param.get('distance_cap', 0),
                    request_param.get('category', ''),
                    request_param.get('amenity', ''),
                    request_param.get('meta_key', ''),
                    request_param.get('latitude', 0),
                    request_param.get('longitude', 0),
                    request_param.get('radius', 0),
                    request_param.get('checkin', ''),
                    request_param.get('checkout', ''),
                    request_param.get('roomconfig', ''),
                    request_param.get('meta_hotel_id', ''))
        if application_id == ApplicationId.WEBSITE:
            key = '_'.join((key, application_id))
        key = key.replace(" ", "_")

        if search_dto.search_channel == GDC:
            return SearchApiV5.GDC_CACHE_KEY.format(key)
        else:
            return SearchApiV5.DIRECT_CACHE_KEY.format(key)

    @staticmethod
    def get_available_room_types_cache_key(hotel_id, check_in, check_out, room_config):
        key = "{0}{1}_checkin_{2}_checkout_{3}_room_config_{4}".format(
            SearchApiV5.AVAILABLE_ROOM_TYPES_CACHE_KEY,
            hotel_id,
            check_in,
            check_out,
            room_config,
        )
        key = key.replace(" ", "_")

        return key

    @staticmethod
    @timed
    def make_response(hotels, category_dict, sort_result, search_type, application_id):
        result = []
        response = {
            'sort': {
                'by': search_type
            },
            'result': []
        }

        couple_and_local_id_policies_all_hotel = HotelPolicyService.get_all_hotel_policies(
            hotels,
            [
                HotelPolicyType.IS_COUPLE_FRIENDLY,
                HotelPolicyType.IS_LOCAL_ID_ALLOWED
            ]
        )
        hotel_ids = [hotel.id for hotel in hotels]
        brand_list_all_hotels = HotelSubBrandService.get_brands_for_hotels(hotel_ids)
        facilities_all_hotels = HotelFacilityService.get_all_hotel_facilities(
            hotels, filter_to_be_shown=True)
        hotel_wise_alias = HotelsRepository().get_hotel_aliases(hotel_ids)
        landmarks_all_hotels = HotelLandmarkService.get_hotel_landmarks(
            hotel_ids, max_distance=Decimal(10))
        nearby_distance_cap = ContentStoreService.get_constant_content(key='nearby_distance_cap',
                                                                       default_value=NEARBY_DISTANCE_CAP)
        near_airport_pois, near_railway_pois = HotelPOIService.get_transportation_pois(hotel_ids,
                                                                                       nearby_distance_cap)
        all_hotel_images = HotelImageService.get_all_hotel_images(hotels)
        bed_types = ContentStoreService.get_constant_content(key='catalog_mapped_bed_types',
                                                             default_value=CATALOG_MAPPED_BED_TYPES)

        hotel_categories_mapping = {}
        hotels_with_categories = HotelsRepository().get_category_for_hotels(hotels)
        for hotel in hotels_with_categories:
            hotel_categories_mapping[hotel.id] = hotel.category.all()

        for hotel in hotels:
            hotel_couple_and_local_id_policy = couple_and_local_id_policies_all_hotel.get(hotel.id, [])
            categories = hotel_categories_mapping.get(hotel.id, [])
            facilities = facilities_all_hotels.get(hotel.id, [])
            amenities, room_amenities, other_amenities, available_bed_types = \
                SearchApiV5._populate_amenities_for_hotel(facilities, bed_types)
            popular_collections, family_hotels_set, beach_hotels_set = \
                SearchApiV5._populate_popular_collections(hotel, categories,
                                                          hotel_couple_and_local_id_policy,
                                                          near_airport_pois, near_railway_pois)

            active_brand = None
            for brand in brand_list_all_hotels.get(hotel.id, []):
                if brand["status"] == "ACTIVE":
                    active_brand = brand["code"]
                    break

            item = {
                'id': hotel.id,
                'cs_id': hotel.cs_id,
                'sort_index': sort_result[hotel.id]['sort_index'],
                'available_room_types': sort_result[hotel.id]['available_room_types'],
                'modified_available_room_types': sort_result[hotel.id]['modified_available_room_types'],
                'distance': round(hotel.distance) if hotel.distance else None,
                'hotel_name': HotelService().get_display_name(hotel,
                                                              hotel_wise_alias.get(hotel.id)),
                'is_active': True if hotel.status else False,
                'area': {
                    'pincode': hotel.locality.pincode,
                    'locality': hotel.locality.name,
                    'city': hotel.city.name,
                    'state': hotel.state.name,
                    'landmarks': landmarks_all_hotels.get(hotel.id, []),
                    'country': 'India'
                },
                'amenities': amenities,
                'images': all_hotel_images.get(hotel.id, []),
                'coordinates': {
                    'lat': hotel.latitude,
                    'lng': hotel.longitude
                },
                'hotel_policies': hotel_couple_and_local_id_policy,
                'is_value_for_money': category_dict[hotel.hotelogix_id]['is_value_for_money'],
                'property': {
                    'provider': hotel.provider_name,
                    'type': hotel.property_type
                },
                'active_brand': active_brand,
                'promoted': sort_result[hotel.id].get('promoted', False),
                'hygiene_shield_name': hotel.hygiene_shield_name,
                'slugify_url': hotel.get_slugified_url(),
                'popular_collections': popular_collections,
                'available_bed_type': available_bed_types,
                'room_amenities': room_amenities,
                'other_amenities': other_amenities,
            }

            result.append(item)
        response['result'] = result
        return response

    @staticmethod
    def _populate_amenities_for_hotel(facilities, bed_types):
        amenities = []
        room_amenities = []
        other_amenities = []
        available_bed_types = set()
        for facility in facilities:
            amenities.append(facility["id"])
            if facility["category_name"].lower() == FacilityCategory.IN_ROOM_FACILITIES.lower():
                if (facility["catalog_mapped_name"].lower() not in EXCLUDE_AMENITIES and
                    facility["catalog_mapped_name"] not in bed_types):
                    room_amenities.append(facility["id"])
            else:
                if (facility["catalog_mapped_name"].lower() not in EXCLUDE_AMENITIES and
                    facility["catalog_mapped_name"] not in bed_types):
                    other_amenities.append(facility["id"])
            available_bed_types.add(facility["name"]) \
                if facility["catalog_mapped_name"] in bed_types else None
        return amenities, room_amenities, other_amenities, available_bed_types

    @staticmethod
    def _populate_popular_collections(hotel, categories, hotel_couple_and_local_policy,
                                      near_airport_pois, near_railway_pois):
        popular_collections = []
        family_hotels_set = False
        beach_hotels_set = False
        for policy in hotel_couple_and_local_policy:
            if policy["policy_type"] == HotelPolicyType.IS_COUPLE_FRIENDLY:
                popular_collections.append(
                    {
                        "description": policy["description"],
                        "type": policy["policy_type"],
                        "title": policy["title"]
                     }
                )

        for category in categories:
            if category.name in POPULAR_CATEGORY_COLLECTION:
                description = category.description
                if (near_airport_pois and category.name == HotelCategory.NEAR_AIRPORT
                    and hotel.id in near_airport_pois):
                    description = str(round(near_airport_pois[hotel.id][1])) + ' kms from ' + \
                                  str(near_airport_pois[hotel.id][0])
                elif near_railway_pois and category.name == HotelCategory.NEAR_CITY_RAILWAY_STATION and hotel.id \
                    in near_railway_pois:
                    description = str(round(near_railway_pois[hotel.id][1])) + 'kms from ' + \
                                  str(near_railway_pois[hotel.id][0])
                popular_collections.append({"description": description,
                                            "type": category.name,
                                            "title": category.name})
            elif category.name in [HotelCategory.FAMILY_HOTELS,
                                   HotelCategory.FAMILY_RESORTS] and not family_hotels_set:
                family_hotels_set = True
                popular_collections.append({"description": "",
                                            "type": "Family hotels",
                                            "title": "Family hotels"})
            elif category.name in [HotelCategory.BEACH_HOTELS,
                                   HotelCategory.BEACH_RESORTS] and not beach_hotels_set:
                beach_hotels_set = True
                popular_collections.append({"description": "",
                                            "type": "Near a beach",
                                            "title": "Near a beach"})
        return popular_collections, family_hotels_set, beach_hotels_set

    @validate_request(validationSerializer)
    @log_args(logger)
    def get(self, request, *args, **kwargs):
        """
        Search API to get list of available hotels for Search Page.
        Model dependency: State, City, Locality, CityAlias, Hotel, Image, Facility
        hotel_id -- Hotel Id
        hotel_name -- Hotel Name
        locality_name -- Locality Name
        city_name -- City Name
        state_name -- State Name
        country_name -- Country Name
        location_type -- Location Type (Select one out of - hotel/locality/city/state/country)
        nearby -- Search Near By Hotels (true/false)
        """
        try:
            search_dto = self.parse_request(request)
        except InvalidBookingDates as err:
            response = APIResponse.treebo_exception_error_response(err)
            return response

        except Exception as e:
            return Response({
                'error': e.__str__(),
                'result': [],
                'sort': {},
            }, status=status.HTTP_400_BAD_REQUEST)
        application_id = request.META.get('HTTP_X_APPLICATION_ID')

        cache_key = self.get_cache_key(request_param=request.GET, search_dto=search_dto,
                                       application_id=application_id)

        result = cache.get(cache_key)
        if result:
            st = datetime.now()
            self._update_available_room_types(result, search_dto)
            tt = (datetime.now() - st).total_seconds()
            headers = {
                "Cached-Response": True,
                "UpdateRoomAvailabilityTime": str(tt) + ' seconds'
            }
            return Response(result, headers=headers)

        try:
            search_result = self._search_hotels(search_dto)
            category_dict = CategoriesServices().category_dict(hotels=search_result.hotels,
                                                               hotel_ids=search_result.hotel_ids)

            sort_type = self._get_sort_type(search_dto)
            sorted_hotels = self._apply_sorting(search_dto, search_result, sort_type)

            response = self.make_response(hotels=search_result.hotels, category_dict=category_dict,
                                          sort_result=sorted_hotels, search_type=sort_type,
                                          application_id=application_id)

            cache.set(cache_key, response, SearchApiV5.CACHE_TIMEOUT)

            return Response(response)

        except NoHotelsFoundException as e:
            SlackAlertService.send_slack_alert_for_search_seo(
                request_param=request.GET, api_name=self.__class__.__name__)
            return Response(data={
                'error': e.message,
                'result': [],
                'filters': {},
                'sort': {},
                'sort_params': "",
            }, status=e.status_code)

        except Exception as e:
            SlackAlertService.send_slack_alert_for_exceptions(
                status=500,
                request_param=request.GET,
                message=e.__str__(),
                class_name=self.__class__.__name__)
            return Response({
                'error': e.__str__,
                'result': [],
                'sort': {},
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _apply_sorting(self, search_dto, search_result, sort_type):
        sku_search_cache_service = SkuSearchCacheService()
        pricing_sorting_service = PricingSortingService()

        room_wise_availability_per_hotel = self._get_availability(
            search_dto.checkin_date, search_dto.checkout_date, search_result.hotels,
            search_dto.room_config, sku_search_cache_service)

        if sort_type == 'distance':
            specific_hotel_searched = None
            if search_dto.hotel_id:
                search_result.hotels = [h for h in search_result.hotels if
                                        h.id != search_dto.hotel_id]
                if not search_dto.nearby:
                    specific_hotel_searched = search_result.filter_hotel(search_dto.hotel_id)

            sorted_hotels = pricing_sorting_service.sort_by_distance_hotels(
                search_result.hotels, room_wise_availability_per_hotel,
                search_result.hotel_web_cs_id_dict, specific_hotel_searched)

            if specific_hotel_searched:
                search_result.hotels = [specific_hotel_searched] + search_result.hotels

        else:
            search_cache_service_result = \
                sku_search_cache_service.get_min_hotels_price_from_cache(
                    search_result.hotel_ids, room_wise_availability_per_hotel,
                    search_result.hotel_web_cs_id_dict)

            sorted_hotels = pricing_sorting_service.sort_recommended_hotels(
                search_cache_service_result, search_result.hotel_ids, search_dto.checkin_date,
                search_dto.checkout_date, search_dto.room_config, search_dto.search_channel,
                room_availability_per_hotel=room_wise_availability_per_hotel,
                hotel_web_cs_id_dict=search_result.hotel_web_cs_id_dict,
                meta_hotel_id=search_dto.meta_hotel_id)
        return sorted_hotels

    @staticmethod
    def _get_availability(check_in_date, check_out_date, hotels, room_config,
                          sku_search_cache_service, cs_ids=None):
        cs_ids = cs_ids or [hotel.cs_id for hotel in hotels]
        if FeatureToggleAPI.is_enabled(settings.DOMAIN, FEATURE_TOGGLE_NAMES.SKU_SEARCH, False):
            availability_data = sku_search_cache_service.get_availability_data(
                cs_ids, check_in_date, check_out_date, room_config)
        else:
            availability_data = ITSAvailabilityService.get_availability_data(
                cs_ids, check_in_date, check_out_date, room_config)

        hotel_wise_data_to_set_in_cache = {}
        for key, value in availability_data.items():
            cache_key = SearchApiV5.get_available_room_types_cache_key(
                hotel_id=key, check_in=check_in_date, check_out=check_out_date,
                room_config=room_config)
            hotel_wise_data_to_set_in_cache[cache_key] = value

        cache.set_many(hotel_wise_data_to_set_in_cache,
                       timeout=SearchApiV5.AVAILABLE_ROOM_TYPES_CACHE_TIMEOUT)

        return availability_data

    def _search_hotels(self, search_dto):
        if FeatureToggleAPI.is_enabled(settings.DOMAIN, FEATURE_TOGGLE_NAMES.REPO_PATTERN, False):
            hotels = HotelsRepository().search(search_dto, prefetch_hotel_aliases=True)
        else:
            search_service = self.get_search_service_object(search_dto)
            hotels = search_service.search()
        return SearchResult(hotels)

    def parse_request(self, request):
        request_param = request.GET
        header_info = request.META.get('HTTP_ORIGIN', DIRECT)

        hotel_id = request_param.get('hotel_id')
        city_name = request_param.get('city')
        landmark_name = request_param.get('landmark')

        if landmark_name and not city_name:
            landmark_name = str(landmark_name)
            landmark_objs = self.search_landmark_repository.get_landmarks_by_freetext_url(
                query_string=landmark_name)
            landmark_obj = landmark_objs[0] if landmark_objs else None
            if not landmark_obj:
                items = landmark_name.split()
                city_name = items[-1]
                landmark_name = ' '.join(items[:-1])
                if len(items) == 2:
                    city_name = items[0]
                    landmark_name = ' '.join(items[1:])

            else:
                city_name = landmark_obj.city.name
                landmark_name = landmark_obj.seo_url_name

        nearby = request_param.get('nearby')
        distance_cap = request_param.get('distance_cap')
        nearby = True if nearby and nearby.lower() == 'true' else False
        distance_cap = True if distance_cap and distance_cap.lower() == 'true' else False

        validated_data = self.serializerObject.validated_data

        search_dto = HotelSearchQuery(
            hotel_id=int(hotel_id) if hotel_id else None,
            locality_name=request_param.get('locality'),
            landmark_name=landmark_name, city_name=city_name, state_name=request_param.get('state'),
            country_name=request_param.get('country'),
            location_type=request_param.get('location_type'), nearby=nearby,
            category=' '.join(request_param.get('category').split('-')) if request_param.get(
                'category') else None,
            amenity=' '.join(request_param.get('amenity').split('-')) if request_param.get(
                'amenity') else None,
            meta_keys=request_param.get('meta_key'),
            latitude=request_param.get('latitude'),
            longitude=request_param.get('longitude'),
            radius=int(request_param.get('radius', settings.DEFAULT_SEO_LANDMARK_RADIUS)),
            distance_cap=distance_cap,
            hotel_name=request_param.get('hotel'),
            channel=request_param.get('channel', DEFAULT_SEARCH_CHANNEL),
            checkin_date=validated_data['checkin'], checkout_date=validated_data['checkout'],
            room_config=validated_data['roomconfig'],
            meta_hotel_id=validated_data.get('meta_hotel_id', None),
            search_channel=GDC if GDC_BASE_HOST_URL in header_info else DIRECT)
        return search_dto

    @staticmethod
    def get_search_service_object(search_dto):
        return SearchService(
            hotel_id=search_dto.hotel_id, hotel_name=search_dto.hotel_name,
            locality_name=search_dto.locality_name,
            landmark_name=search_dto.landmark_name, city_name=search_dto.city_name,
            state_name=search_dto.state_name, country_name=search_dto.country_name,
            location_type=search_dto.location_type, nearby=search_dto.nearby,
            category=search_dto.category, amenity=search_dto.amenity,
            meta_keys=search_dto.meta_keys, latitude=search_dto.latitude,
            longitude=search_dto.longitude, radius=search_dto.radius,
            distance_cap=search_dto.distance_cap)

    @staticmethod
    def _get_sort_type(search_dto):
        if search_dto.hotel_id or search_dto.landmark_name or search_dto.locality_name:
            return 'distance'
        elif search_dto.latitude and search_dto.longitude and search_dto.radius and \
            search_dto.location_type == 'geo':
            return 'distance'
        else:
            return 'recommended'

    @timed
    def _update_available_room_types(self, data, search_dto):
        missing_hotels_map = {}
        try:
            hotels = data["result"]
            cache_key_hotel_map = {}
            all_cache_keys = []
            for hotel in hotels:
                cache_key = self.get_available_room_types_cache_key(
                    hotel_id=hotel['cs_id'], check_in=search_dto.checkin_date,
                    check_out=search_dto.checkout_date, room_config=search_dto.room_config)
                all_cache_keys.append(cache_key)
                cache_key_hotel_map[cache_key] = hotel

            all_available_room_types = cache.get_many(all_cache_keys) or {}
            missing_keys = set(all_cache_keys) - set(all_available_room_types.keys())
            for missing_key in missing_keys:
                missing_hotels_map[cache_key_hotel_map[missing_key]["cs_id"]] = cache_key_hotel_map[
                    missing_key]
            for cache_key, cached_data in all_available_room_types.items():
                if cached_data is not None:
                    cache_key_hotel_map[cache_key][
                        "available_room_types"
                    ] = list(cached_data["available_room_types"])
                    cache_key_hotel_map[cache_key][
                        "modified_available_room_types"
                    ] = cached_data["modified_available_room_types"]
                else:
                    missing_hotels_map[cache_key_hotel_map[cache_key]["cs_id"]] = \
                        cache_key_hotel_map[cache_key]

            if len(missing_hotels_map) > 0:
                self._get_availability(search_dto.checkin_date, search_dto.checkout_date,
                                       [], search_dto.room_config,
                                       SkuSearchCacheService(),
                                       cs_ids=[hotel_id for hotel_id in missing_hotels_map.keys()])
                missing_hotels_cache_keys = []
                for hotel_cs_id, hotel in missing_hotels_map.items():
                    missing_hotels_cache_keys.append(self.get_available_room_types_cache_key(
                        hotel_id=hotel_cs_id, check_in=search_dto.checkin_date,
                        check_out=search_dto.checkout_date, room_config=search_dto.room_config))
                updated_available_room_types = cache.get_many(missing_hotels_cache_keys)
                for cache_key, cached_data in updated_available_room_types.items():
                    if cached_data is not None:
                        cache_key_hotel_map[cache_key][
                            "available_room_types"
                        ] = list(cached_data["available_room_types"])
                        cache_key_hotel_map[cache_key][
                            "modified_available_room_types"
                        ] = cached_data["modified_available_room_types"]
        except Exception as e:
            logger.exception(str(e))
