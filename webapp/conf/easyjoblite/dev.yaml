app_id: DefaultApp
async_timeout: 120
dead_message_log_file: /var/tmp/easyjoblite_dl.log
default_dl_consumer_count: 3
default_retry_consumer_count: 1
default_worker_count: 3
eqc_sleep_duration: 15
health_check_interval: 2
import_paths: .
max_retries: 3
max_worker_count: 8
pid_file_path: /var/tmp/easyjoblite.pid
rabbitmq_url: amqp://guest:guest@localhost:5672//
workers_log_file_path: /var/tmp/easyjoblite.log
rmq_config:
  heartbeat_interval: 5
  prefetch_count: 2
  retry: True
  retry_policy:
    interval_start: 0
    interval_step: 2
    interval_max: 10
    max_retries: 3