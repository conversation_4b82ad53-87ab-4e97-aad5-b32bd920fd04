webpackJsonp([10],{0:function(e,t,a){e.exports=a(360)},360:function(e,t,a){"use strict";function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0}),t.ReferralPage=void 0;var n=a(141),r=l(n),s=a(92),o=l(s),u=a(56),i=l(u),d=a(57),m=l(d),f=a(69),c=l(f),p=a(59),b=l(p),g=a(58),h=l(g),_=a(8),E=l(_),v=a(9),y=l(v),N=a(22),S=l(N),w=a(7),P=l(w),C=a(63),k=a(45),O=l(k),F=a(3),T=l(F);a(19);var M=a(26),D=l(M),q=a(14),G=l(q);a(473);var x=(0,T.default)("#commonLoader"),R=(0,E.default)(y.default),I=JSON.parse((0,T.default)("#referrerDetails").val()||"{}"),U=!G.default.isEmpty(I);R.pageLoaded=function(){rt.page=U?"referral":"",R.render()};var L=function(e){var t=e.onFbSignup;return P.default.createElement("div",{className:"signup-social row"},P.default.createElement("button",{onClick:t,className:"btn btn--facebook col col-1"},P.default.createElement("i",{className:"icon-fb"}),"| Facebook"),P.default.createElement("button",{id:"googleReferralSignup",className:"btn btn--google col col-2"},P.default.createElement("i",{className:"icon-google"}),"| Google"))},V=function(e){var t=e.onEmailSignup;return P.default.createElement("div",{className:"signup-social"},P.default.createElement("button",{onClick:t,className:"btn btn--email"},P.default.createElement("i",{className:"icon-mail"}),"| Sign up with Email"))},A=function(e){function t(){return(0,m.default)(this,t),(0,b.default)(this,(t.__proto__||(0,i.default)(t)).apply(this,arguments))}return(0,h.default)(t,e),(0,c.default)(t,[{key:"componentDidMount",value:function(){var e=this;O.default.getGoogleHandler().then(function(t){t.attachClickHandler("googleReferralSignup",{},e.props.onGoogleSignup.bind(e),e.onGoogleSignupFail.bind(e))})}},{key:"onGoogleSignupFail",value:function(e){throw e}},{key:"render",value:function(){return P.default.createElement("div",null,P.default.createElement("h3",{className:"referral__signup-text"},"Signup with Treebo"),P.default.createElement("p",{className:"referral__block-text"},"EASILY USING"),P.default.createElement(L,{onFbSignup:this.props.onFbSignup}),P.default.createElement("p",{className:"referral__block-text"},"OR USING EMAIL"),P.default.createElement(V,{onEmailSignup:this.props.onEmailSignup}),U?null:P.default.createElement("p",{className:"referral__block-text referral__block-text__login"},"Already have an account? ",P.default.createElement("a",{className:"referral__link",href:"/login"},"Login!")))}}]),t}(P.default.Component),H=function(e){function t(e){(0,m.default)(this,t);var a=(0,b.default)(this,(t.__proto__||(0,i.default)(t)).call(this,e));return a.handleFieldChange=function(e){a.setState((0,o.default)({},e.target.name,e.target.value))},a.state=a.props.user,a}return(0,h.default)(t,e),(0,c.default)(t,[{key:"componentDidMount",value:function(){var e=this;(0,T.default)("#signupForm").parsley().on("form:submit",function(t){t.submitEvent.preventDefault();var a=e.state;return e.props.setUserDetails(a),e.props.onSignup(),!1})}},{key:"render",value:function(){return P.default.createElement("form",{id:"signupForm",className:"signup-form"},P.default.createElement("div",{className:"signup__error error"}),P.default.createElement("div",{className:"float-labels"},P.default.createElement("input",{onChange:this.handleFieldChange,name:"name",className:"float-labels__input",type:"text",value:this.state.name||"",placeholder:"Name",pattern:".+",required:"true","data-parsley-required":"true","data-parsley-required-message":"Please enter name here"}),P.default.createElement("label",{className:"float-labels__label"},"Name")),P.default.createElement("div",{className:"login__full float-labels"},P.default.createElement("input",{onChange:this.handleFieldChange,name:"mobile",className:"float-labels__input",pattern:".+",type:"number",value:this.state.mobile||"",placeholder:"Mobile Number",required:"true","data-parsley-type":"number","data-parsley-trigger":"change","data-parsley-type-message":"Enter valid mobile number","data-parsley-required-message":"Please enter valid mobile number","data-parsley-length-message":"Please enter 10 digit mobile number","data-parsley-minlength":"10","data-parsley-maxlength":"10"}),P.default.createElement("label",{className:"float-labels__label"},"Mobile Number")),P.default.createElement("div",{className:"float-labels"},P.default.createElement("input",{onChange:this.handleFieldChange,name:"email",className:"float-labels__input",type:"email",pattern:".+",value:this.state.email||"",placeholder:"Email ID",required:"true","data-parsley-type":"email","data-parsley-trigger":"change","data-parsley-type-message":"Enter valid email","data-parsley-required-message":"Please enter a valid email address"}),P.default.createElement("label",{className:"float-labels__label"},"Email ID")),P.default.createElement("div",{className:"float-labels"},P.default.createElement("input",{onChange:this.handleFieldChange,name:"password",className:"float-labels__input",type:"password",pattern:".+",value:this.state.password||"",placeholder:"Password",required:"true","data-parsley-minlength":"6","data-parsley-required-message":"Oops! You forgot to enter your password"}),P.default.createElement("label",{className:"float-labels__label"},"Password")),P.default.createElement("button",{type:"submit",className:"btn signup-form__submit"},"SIGN UP"))}}]),t}(P.default.Component),J=function(e){function t(e){(0,m.default)(this,t);var a=(0,b.default)(this,(t.__proto__||(0,i.default)(t)).call(this,e));return a.handleFieldChange=function(e){var t;a.setState((t={},(0,o.default)(t,e.target.name,e.target.value),(0,o.default)(t,"error",""),t))},a.state={otp:"",error:""},a}return(0,h.default)(t,e),(0,c.default)(t,[{key:"componentDidMount",value:function(){var e=this;(0,T.default)("#otpForm").parsley().on("form:submit",function(t){return t.submitEvent.preventDefault(),e.props.onVerifyOTP(e.state.otp),!1})}},{key:"render",value:function(){var e=this.props,t=e.user,a=e.onEditMobile,l=e.onSendOTP;return P.default.createElement("div",{className:"popup"},P.default.createElement("div",{className:"modal pos-abs"},P.default.createElement("div",{className:"modal__body"},P.default.createElement("p",{className:"popup__title"},"Enter One Time Passcode"),P.default.createElement("p",{className:"popup__subtitle"},"OTP sent to ",t.mobile," ",P.default.createElement("small",{onClick:a,className:"popup__edit"},"change number")),P.default.createElement("form",{id:"otpForm"},P.default.createElement("div",{className:"float-labels"},P.default.createElement("small",{className:"float-labels__input-link",onClick:l},"Resend OTP"),P.default.createElement("input",{onChange:this.handleFieldChange,name:"otp",className:"float-labels__input",type:"number",value:this.state.otp||"",placeholder:"One Time Passcode",pattern:".+",required:"true","data-parsley-type":"number","data-parsly-trigger":"change","data-parsley-type-message":"Enter 6 digit code","data-parsley-required":"true","data-parsley-required-message":"Please enter OTP here"}),P.default.createElement("label",{className:"float-labels__label"},"One Time Passcode")),P.default.createElement("button",{type:"submit",className:"btn popup__submit"},"Verify")))))}}]),t}(P.default.Component),Y=function(e){function t(e){(0,m.default)(this,t);var a=(0,b.default)(this,(t.__proto__||(0,i.default)(t)).call(this,e));return a.handleFieldChange=function(e){a.setState((0,o.default)({},e.target.name,e.target.value))},a.state={mobile:a.props.user.mobile||""},a}return(0,h.default)(t,e),(0,c.default)(t,[{key:"componentDidMount",value:function(){var e=this;(0,T.default)("#mobileForm").parsley().on("form:submit",function(t){return t.submitEvent.preventDefault(),e.props.onSendOTP(e.state.mobile),!1})}},{key:"render",value:function(){this.props.user;return P.default.createElement("div",{className:"popup"},P.default.createElement("div",{className:"modal pos-abs"},P.default.createElement("div",{className:"modal__body"},P.default.createElement("p",{className:"popup__title"},"Enter Mobile Number"),P.default.createElement("p",{className:"popup__subtitle"},"We will send you an SMS with a One Time Password"),P.default.createElement("form",{id:"mobileForm"},P.default.createElement("div",{className:"float-labels"},P.default.createElement("input",{onChange:this.handleFieldChange,name:"mobile",className:"float-labels__input",pattern:".+",type:"number",value:this.state.mobile||"",placeholder:"Mobile Number",required:"true","data-parsley-type":"number","data-parsley-trigger":"change","data-parsley-type-message":"Enter valid mobile number","data-parsley-required-message":"Please enter valid mobile number","data-parsley-length-message":"Please enter 10 digit mobile number","data-parsley-minlength":"10","data-parsley-maxlength":"10"}),P.default.createElement("label",{className:"float-labels__label"},"Mobile Number")),P.default.createElement("button",{type:"submit",className:"btn popup__submit"},"Submit")))))}}]),t}(P.default.Component),j=t.ReferralPage=function(e){function t(e){(0,m.default)(this,t);var a=(0,b.default)(this,(t.__proto__||(0,i.default)(t)).call(this,e));return a.onFbSignup=function(){O.default.fbLogin().then(function(e){e.register&&U?a.setState({user:(0,r.default)({},a.state.user,{name:e.name,email:e.email}),showMobile:!0}):window.location="/"})},a.onGoogleSignup=function(e){O.default.sendGoogleRegistrationDataToServer(e.getAuthResponse().id_token).then(function(e){e.register&&U?a.setState({user:(0,r.default)({},a.state.user,{name:e.name,email:e.email}),showMobile:!0}):window.location="/",x.addClass("hide")})},a.onSignup=function(){var e=(0,T.default)(".signup__error");e.html(""),O.default.signup(a.state.user,function(t){throw t.redirect?window.location="/login/":e.html(t.message),t}).then(function(){U?a.setState({showOTP:!0}):window.location="/"})},a.onEmailSignup=function(){a.setState({showSignupForm:!0})},a.onSendOTP=function(e){x.removeClass("hide"),e="string"==typeof e?e:a.state.user.mobile,(0,S.default)({url:"/api/v1/auth/otp/",type:"POST",data:{mobile:e,csrfmiddlewaretoken:D.default.get("csrftoken")}}).then(function(){a.setState({user:(0,r.default)({},a.state.user,{mobile:e}),showMobile:!1,showOTP:!0})}).finally(function(){x.addClass("hide")})},a.onEditMobile=function(){a.setState({showOTP:!1,showMobile:!0})},a.onVerifyOTP=function(e){x.removeClass("hide"),(0,S.default)({url:"/api/v1/auth/verify/",type:"POST",data:{otp:e,mobile:a.state.user.mobile,csrfmiddlewaretoken:D.default.get("csrftoken")}}).then(function(){window.location="/"}).catch(function(e){throw e}).finally(function(){x.addClass("hide")})},a.setUserDetails=function(e){a.setState({user:(0,r.default)({},a.state.user,e)})},a.state={user:{promo:""},showSignupForm:!1,showOTP:!1,showMobile:!1},a}return(0,h.default)(t,e),(0,c.default)(t,[{key:"render",value:function(){var e=this.state,t=e.user,a=e.showSignupForm,l=e.showOTP,n=e.showMobile;return P.default.createElement("div",{className:"referral"},a?P.default.createElement(H,{user:t,setUserDetails:this.setUserDetails,onSignup:this.onSignup}):P.default.createElement(A,{onFbSignup:this.onFbSignup,onGoogleSignup:this.onGoogleSignup,onEmailSignup:this.onEmailSignup}),n?P.default.createElement(Y,{user:t,onSendOTP:this.onSendOTP}):null,l?P.default.createElement(J,{user:t,onEditMobile:this.onEditMobile,onVerifyOTP:this.onVerifyOTP,onSendOTP:this.onSendOTP}):null)}}]),t}(P.default.Component);R.render=function(){(0,C.render)(P.default.createElement(j,null),document.getElementById("referralPage"))},R.setup({name:"Referral Page",widgets:[]})},473:460});
//# sourceMappingURL=referral-82054ba4.min.js.map