// Fontsize
// IE fallbak for pixel
.font-size(@font-size: 16) {
    @rem: (@font-size / 16);
    font-size: ~"@{rem}rem";
}

.center(@horizontal: true;
@vertical: true) {
    position: absolute;
    & when(@horizontal=true) and (@vertical=false) {
        left: 50%;
        transform: translate(-50%, 0%);
    }
    & when(@horizontal=false) and (@vertical=true) {
        top: 50%;
        transform: translate(0%, -50%);
    }
    & when(@horizontal=true) and (@vertical=true) {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

.placeholder(@color: #ccc) {
    &:-moz-placeholder {
        color: @color;
    } // Firefox 4-18
    &::-moz-placeholder {
        color: @color;
    } // Firefox 19+
    &:-ms-input-placeholder {
        color: @color;
    } // Internet Explorer 10+
    &::-webkit-input-placeholder {
        color: @color;
    } // Safari and Chrome
}

// Use with max-width & display inline-block
.text-truncate() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@keyframes fillToRight {
    to {
        width: 100%;
    }
}

.btn-loading-mixin(@color, @bg-color, @loading-bg-color) {
    background-color: @bg-color;
    z-index: 0;
    overflow: hidden;
    cursor: not-allowed;
    &:after {
        content: "";
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        z-index: -1;
        width: 0;
        background: @loading-bg-color;
        transition: none;
        animation: fillToRight 1s linear infinite;
    }
}
