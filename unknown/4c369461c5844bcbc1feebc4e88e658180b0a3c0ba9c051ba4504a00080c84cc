from marshmallow import Schema, fields, validates_schema, ValidationError, post_load, validate
from treebo_commons.utils import dateutils


class RatePushSchema(Schema):
    hotel_id = fields.String(required=False, allow_none=True)
    from_date = fields.Date(required=True,
                            error_messages={
                                'null': 'From date may not be null.',
                                'required': 'Please provide from date.',
                                'validator_failed': "'{input}' is not a valid value for from date."
                            })
    to_date = fields.Date(required=True,
                          error_messages={
                              'null': 'To date may not be null.',
                              'required': 'Please provide to date.',
                              'validator_failed': "'{input}' is not a valid value for to date."
                          })
    rate_plan_id = fields.String(required=False, allow_none=True)
    room_type_id = fields.String(required=False, allow_none=True)
    external_hotel_id = fields.String()

    @validates_schema
    def validate_data(self, data, **kwargs):
        if (data.get('rate_plan_id') or data.get('room_type_id')) and not data.get('hotel_id'):
            raise ValidationError("Hotel ID should be provided for rate push for specific rate plan or room type")
        if data['from_date'] < dateutils.current_date():
            raise ValidationError("from_date should be greater than current date")
        if data['to_date'] < dateutils.current_date():
            raise ValidationError("to_date should be greater than current date")
        if (data['to_date'] - data['from_date']).days > 90:
            raise ValidationError("Manual price sync should be in 90 days batch")


class GHARatePushSchema(Schema):
    hotel_ids = fields.List(fields.String(), required=True, validate=validate.Length(max=50))
    from_date = fields.Date(required=False, missing=dateutils.current_date())
    to_date = fields.Date(required=False)
    rate_plan_id = fields.String(required=False, allow_none=True)

    @validates_schema
    def validate_data(self, data, **kwargs):
        if data['from_date'] < dateutils.current_date():
            raise ValidationError("from_date should be greater than current date")
        if data.get('to_date') and data['to_date'] < dateutils.current_date():
            raise ValidationError("to_date should be greater than current date")
        if data.get('to_date') and data['to_date'] < data['from_date']:
            raise ValidationError("to_date should be greater than or equal to from_date")
        if data.get('to_date') and ((data['to_date'] - data['from_date']).days > 90):
            raise ValidationError("Manual price sync should be in 90 days batch")

    @post_load
    def convert_date_to_string(self, data, **kwargs):
        data['to_date'] = dateutils.date_to_ymd_str(
            data.get('to_date') or dateutils.add(data['from_date'], days=90)
        )
        data['from_date'] = dateutils.date_to_ymd_str(data['from_date'])
        return data
