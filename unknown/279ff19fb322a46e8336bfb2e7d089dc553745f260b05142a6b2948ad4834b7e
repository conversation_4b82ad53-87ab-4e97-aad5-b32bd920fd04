import pytest

from tests.factories.catalog_factory import (
    ModularSKUFactory,
    PropertyFactory,
    PropertySKUFactory,
)
import json
from tests.factories.guardrail_factory import GuardRailFactory


class TestGuardRailAPI:
    @staticmethod
    def test_guardrail_create_api(client):
        property = PropertyFactory(
            name="LA Grand", code="123", unirate_id="123", hx_id="123"
        )
        property.save()
        sku = ModularSKUFactory(name="SKU-1", code="sku-1", modular=True)
        sku.save()
        guardrail = GuardRailFactory(property_code="123", sku_code="sku-1")
        guardrail.save()
        payload = """{
                      "property_code": "123",
                      "channel_code": "CH",
                      "sub_channel_code_list": ["SCH", "Trivago"],
                      "sku_code": "sku-1",
                      "min_price": 909,
                      "max_price": 9090
                    }
        """
        response = client.post(
            "/v1/guardrails/", data=payload, content_type="application/json"
        )
        assert response.status_code == 201

    @staticmethod
    def test_guardrail_creation_when_no_channel_and_sub_channel(client):
        sku = ModularSKUFactory(name="SKU-1", code="sku-1", modular=True)
        sku.save()
        guardrail = GuardRailFactory(property_code="123", sku_code="sku-1")
        guardrail.save()
        payload = """{
                      "property_code": "123",
                      "sku_code": "sku-1",
                      "min_price": 909,
                      "max_price": 9090
                    }
                  """
        response = client.post(
            "/v1/guardrails/", data=payload, content_type="application/json"
        )
        assert response.status_code == 201

    @staticmethod
    def test_guard_rail_creation_fails_if_channel_min_max_outside_global_min_max(
        client
    ):
        sku = ModularSKUFactory(name="SKU-1", code="sku-1", modular=True)
        sku.save()
        guardrail = GuardRailFactory(property_code="123", sku_code="sku-1")
        guardrail.save()
        payload = """{
                          "property_code": "123",
                          "channel_code": "CH",
                          "sub_channel_code_list": ["SCH", "Trivago"],
                          "sku_code": "sku-1",
                          "min_price": 400,
                          "max_price": 9090
                        }
                """
        response = client.post(
            "/v1/guardrails/", data=payload, content_type="application/json"
        )
        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert (
            response_data.get("errors")[0].get("message")
            == "GuardRail For: property code 123, sku_code: sku-1, channel_code: CH, sub_channel_code: SCH outside global bounds."
        )

    @staticmethod
    def test_guard_rail_creation_fails_if_global_min_max_not_present(client):
        payload = """{
                          "property_code": "123",
                          "channel_code": "CH",
                          "sub_channel_code_list": ["SCH", "Trivago"],
                          "sku_code": "sku-1",
                          "min_price": 400,
                          "max_price": 9090
                        }
                """
        response = client.post(
            "/v1/guardrails/", data=payload, content_type="application/json"
        )
        assert response.status_code == 400
