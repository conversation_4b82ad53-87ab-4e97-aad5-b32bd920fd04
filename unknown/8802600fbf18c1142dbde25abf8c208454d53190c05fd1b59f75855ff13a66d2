from collections import defaultdict
from typing import List, Union

from flask import current_app

from core.common.api.client.api_request import JsonAPIRequest
from core.common.utils.date import ist_now
from core.external.catalog import catalog_client
from core.external.catalog.sku_room_type_details import SKURoomTypeDetailsRequest
from core.property.data_classes.property import Property
from core.rates.data_classes.price import Price

config = current_app.config


class RatePushRequest(JsonAPIRequest):
    url = f"cm/api/v3/tenants/{config['CM_TENANT']}/sell-prices/"
    method = 'post'

    def success_code_list(self) -> Union[None, list]:
        return [200, 201, 202, 400]

    def __init__(self, property: Property, prices: List[Price]):
        self.property = property
        self.prices = prices
        super().__init__()

    def data(self):
        sku_codes = set([p.sku.uid for p in self.prices])
        meta_info = {sku_code: self._room_type_info_from_sku(sku_code) for sku_code in sku_codes}

        sku_codes_without_children = set([sku_code for sku_code, room_type_info in meta_info.items()
                                          if not room_type_info['max_children']])

        # cm does not deal with skus with children
        prices_for_skus_without_children = [p for p in self.prices if p.sku.uid in sku_codes_without_children]

        grouped_prices_by_room_type = defaultdict(list)
        for p in prices_for_skus_without_children:
            room_type_name = meta_info[p.sku.uid]['room_type_name']
            grouped_prices_by_room_type[room_type_name].append(p)

        hotel_payloads = []
        for room_type, prices_for_room_type in grouped_prices_by_room_type.items():
            # NOTE : There are few skus for which room type is null
            if not room_type:
                continue
            promotions, rates = [], []
            unique_dates_in_prices = set([dp.date for p in prices_for_room_type for dp in p.date_prices])
            for date in unique_dates_in_prices:
                promotion_data, promo_percent = self._promotion_from_date_price(
                    date, prices_for_room_type, room_type, meta_info)
                promotions.append(promotion_data)
                rates_data = self._rate_from_date_price(date, prices_for_room_type, room_type, meta_info, promo_percent)
                rates.append(rates_data)

            room_type_payload = {
                'hotel_code': f'CM-{self.property.uid}',
                'promotions': promotions,
                'rates': rates,
            }
            hotel_payloads.append(room_type_payload)

        return {'sell_prices': hotel_payloads}

    def _promotion_from_date_price(self, date, prices_for_room_type, room_type, meta_info):
        # promo applies on smallest sku only.
        price_and_max_adult_map = {meta_info[p.sku.uid]['max_adults']: p for p in prices_for_room_type}
        price_for_smallest_sku = price_and_max_adult_map[min(price_and_max_adult_map.keys())]
        date_price = price_for_smallest_sku[date]
        promo_percent = round((date_price.list_price - date_price.sell_price) / date_price.list_price * 100, 2)
        return dict(
            promo_id=date_price.uid,
            description=f'Flat {promo_percent}% off',
            promo_type='Basic',
            promo_level='hotel',
            room_code=[f'treebo-{room_type.lower()}'],
            rate_plan_codes=[],
            is_refundable=True,
            promo_conditions=dict(),
            promo_timestamp=ist_now().isoformat(),
            stay_period=dict(
                start=date.isoformat(),
                end=date.isoformat(),
            ),
            discount=dict(
                discount_type='Percent',
                discount_value=str(promo_percent)
            )
        ), promo_percent

    def _rate_from_date_price(self, date, prices_for_room_type, room_type, meta_info, promo_percent):
        """ Inflates the cm price as per the promo percent since cm will discount them"""
        inflation_factor = 100 / (100 - promo_percent)

        return dict(
            currency_code='INR',
            rate_plans=[dict(
                rate_plan_code='treebo-standard',
                stay_dates=dict(
                    start=date.isoformat(),
                    end=date.isoformat(),
                ),
                rates=[dict(
                    room_type_code='treebo-' + room_type.lower(),
                    extra_person_charge=0,
                    occupancy_rates=[dict(
                        occupancy=meta_info[price.sku.uid]['max_adults'],
                        rate=str(round(price[date].sell_price * inflation_factor, 2))
                    ) for price in prices_for_room_type]
                )]
            )],
            rate_timestamp=ist_now().isoformat()
        )

    def _room_type_info_from_sku(self, sku_code):
        sku_detail_request = SKURoomTypeDetailsRequest(sku_code)
        response = catalog_client.execute(sku_detail_request)
        return response.data
