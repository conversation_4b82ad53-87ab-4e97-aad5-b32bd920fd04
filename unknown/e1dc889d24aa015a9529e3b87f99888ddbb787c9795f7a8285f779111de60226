import pytest
from treebo_commons.request_tracing import generate_request_id
from treebo_commons.request_tracing.context import setup_request_context_from_request_headers

from tenant_gateway.app import create_app
from tenant_gateway.infrastructure.database.repositories.hotel_repository import HotelRepository


@pytest.fixture()
def app_client():
    flask_app = create_app()
    request_id = generate_request_id()
    tenant_id = "ten100"
    request_headers = {"X-Tenant-Id": tenant_id, "X-Request-Id": request_id}
    flask_app.before_request_funcs = setup_request_context_from_request_headers(request_headers=request_headers)
    print("===========> Using client fixture")
    test_client = flask_app.test_client()
    yield test_client
    print("===========> Teardown client fixture")


@pytest.fixture()
def dummy_hotel_dto():
    return HotelRepository().load("9909785", "treebo");
