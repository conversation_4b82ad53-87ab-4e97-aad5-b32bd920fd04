<html>
<head>
    <title>Rackrate Tracer</title>
    <link href="//netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.min.css" rel="stylesheet"
          type="text/css"/>
    <link rel="stylesheet" type="text/css"
          href="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.2.4/semantic.min.css">
    <link href="https://cdn.rawgit.com/mdehoog/Semantic-UI-Calendar/76959c6f7d33a527b49be76789e984a0a407350b/dist/calendar.min.css"
          rel="stylesheet" type="text/css"/>
    <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.2.4/semantic.min.js"></script>
    <script src="https://cdn.rawgit.com/mdehoog/Semantic-UI-Calendar/76959c6f7d33a527b49be76789e984a0a407350b/dist/calendar.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.ui.accordion').accordion();
            var property_code = $('#property_code').val();
            var sku_code = $('#sku_code').val();
            var initial_data = {
                            {% for k, v in form.data.items() %}
                            "{{ k }}": "{{ v }}"{% if not loop.last %},{% endif %}
                            {% endfor %}
            }
            $('#rangestart').calendar({
                type: 'date',
                endCalendar: $('#rangeend'),
                today: true,
                formatter: {
                    date: function(date, settings) {
                        return date.toISOString().slice(0, 10);
                    },
                },
                parser: {
                    date: function (text, settings) {
                    return new Date(text);
                    }
                },
            });
            $('#rangeend').calendar({
                type: 'date',
                startCalendar: $('#rangestart'),
                today: true,
                formatter: {
                    date: function(date, settings) {
                        return date.toISOString().slice(0, 10);
                    },
                },
                parser: {
                    date: function (text, settings) {
                    return new Date(text);
                    }
                },
            });

            $('.ui.form')
                .form({
                    fields: {
                        property_code: 'empty',
                        sku_code: 'empty',
                        stay_start: 'empty',
                        stay_end: 'empty',
                        channel_code: ['minLength[3]', 'empty'],
                        sub_channel_code: ['minLength[3]', 'empty'],
                        policy_list: ['minLength[2]', 'empty']
                    }
                });

            $('#property_code').dropdown({sortSelect: true,
                fullTextSearch: 'exact',
                onChange(value) {
                    trigger_sku_dropdown(value);
                }
            });

            trigger_property_dropdown();

             window.initial_data = initial_data

            $('#sku_code').dropdown({sortSelect: true});

            function trigger_sku_dropdown(property_code) {

                $('#sku_code').dropdown('clear');
                $('#sku_code').empty();
                $.ajax({
                    type: "GET",
                    url: '/v1/properties/' + property_code + '/skus/',
                    success: function(response) {
                        for (var key in response) {
                            if (!response.hasOwnProperty(key)) {
                                //The current property is not a direct property of p
                                continue;
                            }
                            var el = response[key]
                            $('#sku_code').append($("<option></option>").attr("value", el.code).text(el.name));
                        }
                        if (initial_data.sku_code){
                                setTimeout(() => {
                           $('#sku_code').dropdown('set selected', initial_data.sku_code);
                            }, 300);

                        };

                    }
                });
            }

            function trigger_property_dropdown() {
                $.ajax({
                    type: "GET",
                    url: '/v1/properties/',
                    success: function(response) {
                        for (var key in response) {
                            if (!response.hasOwnProperty(key)) {
                                //The current property is not a direct property of p
                                continue;
                            }
                            var el = response[key]
                            $('#property_code').append($("<option></option>").attr("value", el.code).text(el.name));
                        }
                        if (initial_data.property_code){
                            setTimeout(() => {
                           $('#property_code').dropdown('set selected', initial_data.property_code);
                           trigger_sku_dropdown(initial_data.property_code);
                            }, 300);

                        };
                    }
                });
            }
        })

        function burst_cache(){
            var property_code = $("#property_code :selected").val();
            $.ajax({
                type: "POST",
                url: '/v1/burst_cache/',
                contentType: 'application/json',
                data: JSON.stringify({property_code: property_code}),
                success: function(response){
                    $("#burst_cache_button").text("CACHE BURSTED");
                    $("#burst_cache_button").css({"color": "blue"});
                },
                error: function(){
                    $("#burst_cache_button").text("CACHE BURST FAIL");
                }
            })
        }
    </script>
</head>
<body>
<div class="ui grid container">
    <div class="ui centered row">
        <form class="ui form error" action="" method="post" enctype="multipart/form-data">
            {{ form.csrf_token }}
            <h2 class="ui dividing header">Trace Rackrate Search</h2>

            <div class="two fields">
                <div class="required field">
                    {{ form.property_code.label }}
                    {{form.property_code(class_="ui search dropdown")}}
                </div>

                <div class="required field">
                    {{ form.sku_code.label }}
                    {{form.sku_code(class_="ui search dropdown")}}
                </div>
            </div>


            <div class="two fields">
                <div class="required field">
                    {{form.stay_start.label}}
                    <div class="ui calendar" id="rangestart">
                        <div class="ui input left icon">
                            <i class="calendar icon"></i>
                            {{form.stay_start}}
                        </div>
                    </div>
                </div>
                <div class="required field">
                    {{form.stay_end.label}}
                    <div class="ui calendar" id="rangeend">
                        <div class="ui input left icon">
                            <i class="calendar icon"></i>
                            {{form.stay_end}}
                        </div>
                    </div>
                </div>

            </div>

            <div class="three fields">
                <div class="required field">
                    {{ form.channel_code.label }}
                    {{form.channel_code}}
                </div>

                <div class="required field">
                    {{ form.sub_channel_code.label }}
                    {{form.sub_channel_code}}
                </div>

                <div class="required field">
                    {{ form.policy_code.label }}
                    {{form.policy_code}}
                </div>
            </div>

            <button class="ui button">Submit</button>

            {% if form.errors %}
            <div class="ui error message">
                <div class="header">Errors:</div>
                <p>{{form.errors}}</p>
            </div>
            {% endif %}

        </form>
    </div>
    <div class="ui hidden divider"></div>

    {% if data.trace_details %}
    <div class="ui centered row">
        {% for property_code, sku_wise_prices in data.trace_details.items() %}
        {% for sku_code, policy_wise_prices in sku_wise_prices.items() %}
        {% for policy_code, date_wise_prices in policy_wise_prices.items() %}
        <table class="ui selectable table">
            <thead>
            <tr class="center aligned">
                <th>
                    DATE
                </th>
                <th>
                    LIST_PRICE
                </th>
                <th>
                    SALE_PRICE
                </th>
            </tr>

            </thead>
            <tbody>
            {% for date, prices in date_wise_prices.items() %}

            <tr>
                <td data-label="DATE" class="collapsing">
                    {{date}}
                </td>
                <td data-label="LIST_PRICE" class="collapsing">
                    {% set price = prices.LP %}
                    <div class="ui styled fluid accordion">
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Override details {% if price.override_id %} * {% endif %}

                        </div>
                        <div class="content">
                            {% if price.override_id %}
                            Override Found: True
                            {% else %}
                            Override Found: False
                            {% endif %}
                            <br>
                            Override id: {{price.override_id}}
                            <br>
                            override_source: {{price.override_source}}
                            <br>
                            override_last_changed: {{price.override_last_changed}}
                            <br>
                        </div>
                        <div class="active title">
                            <i class="dropdown icon"></i>
                            Price details
                        </div>
                        <div class="active content">
                            price_without_multiplier: {{price.price_without_multiplier}}
                            <br>
                            multiplier: {{price.multiplier}}
                            <br>
                            constant: {{price.constant}}
                            <br>
                            price_with_multiplier: {{price.price_with_multiplier}}
                            <br>
                        </div>
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Meta
                        </div>
                        <div class="content">
                            lrp_applied: {{price.lrp_applied}}
                            <br>
                            default_lrp_found: {{price.default_lrp_found}}
                            <br>
                            specific_lrp_found: {{price.specific_lrp_found}}
                            <br>
                            override_type: {{price.override_type}}
                            <br>
                        </div>
                        {% if price.child_breakup %}
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Child breakup
                        </div>
                        <div class="content">
                            {% for sku_code, price_breakup in price.child_breakup.items() %}
                            sku_name: {{data.sku_details.get(sku_code)}}
                            <br>
                            sku_code: {{sku_code}}
                            <br>
                            price: {{price_breakup.price}} ({{price_breakup.price_type}})
                            <br>
                            sku_count: {{price_breakup.count}}
                            <br>
                            <br>
                            {% endfor %}
                        </div>
                        {% if data.burst_cache %}
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Api Details
                        </div>
                        <div class="content">
                            Rackrate API Price: {{ data.rackrate_and_tracer_price_diff[date]["api_lp"] }}
                        </div>
                        {% endif %}
                        {% endif %}
                    </div>
                </td>
                <td data-label="SALE_PRICE" class="collapsing">
                    {% set price = prices.SP %}
                    <div class="ui styled fluid accordion">
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Override details {% if price.override_id %} * {% endif %}

                        </div>
                        <div class="content">
                            {% if price.override_id %}
                            Override Found: True
                            {% else %}
                            Override Found: False
                            {% endif %}
                            <br>
                            Override id: {{price.override_id}}
                            <br>
                            override_source: {{price.override_source}}
                            <br>
                            override_last_changed: {{price.override_last_changed}}
                            <br>
                        </div>
                        <div class="active title">
                            <i class="dropdown icon"></i>
                            Price details
                        </div>
                        <div class="active content">
                            price_without_multiplier: {{price.price_without_multiplier}}
                            <br>
                            multiplier: {{price.multiplier}}
                            <br>
                            constant: {{price.constant}}
                            <br>
                            price_with_multiplier: {{price.price_with_multiplier}}
                            <br>
                        </div>
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Meta
                        </div>
                        <div class="content">
                            lrp_applied: {{price.lrp_applied}}
                            <br>
                            default_lrp_found: {{price.default_lrp_found}}
                            <br>
                            specific_lrp_found: {{price.specific_lrp_found}}
                            <br>
                            override_type: {{price.override_type}}
                            <br>
                        </div>
                        {% if price.child_breakup %}
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Child breakup
                        </div>
                        <div class="content">
                            {% for sku_code, price_breakup in price.child_breakup.items() %}
                            sku_name: {{data.sku_details.get(sku_code)}}
                            <br>
                            sku_code: {{sku_code}}
                            <br>
                            price: {{price_breakup.price}} ({{price_breakup.price_type}})
                            <br>
                            sku_count: {{price_breakup.count}}
                            <br>
                            <br>
                            {% endfor %}
                        </div>
                        {% if data.burst_cache %}
                        <div class="title">
                            <i class="dropdown icon"></i>
                            Api Details
                        </div>
                        <div class="content">
                            Rackrate API Price: {{ data.rackrate_and_tracer_price_diff[date]["api_sp"] }}
                        </div>
                        {% endif %}
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
        {% if data.burst_cache %}
        <button class="ui button" id="burst_cache_button" style="color:red" onclick="burst_cache()">BURST CACHE</button>
        {% endif %}
        {% endfor %}
        {% endfor %}
        {% endfor %}
    </div>
    {% endif %}
</div>
</body>
</html>