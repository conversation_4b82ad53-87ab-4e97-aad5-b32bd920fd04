import logging

from athena.api.exceptions import HotelsDoesNotExists
from b2b import constants
from b2b.models import Hotel, HotelAttribute, State
from b2b.domain.services import HotelService as B2BHotelService

logger = logging.getLogger(__name__)


class HotelService(object):

    @classmethod
    def get_hotels(cls, **kwargs):
        # Right now sales poc id is same as user id. Might need to separate it out as different logical entity
        sales_poc_email = None
        if kwargs.get('user_email'):
            sales_poc_email = kwargs.get('user_email')

        hotels = Hotel.objects.filter(active=True).values('id', 'hotel_id', 'name', 'city', 'external_id',
                                                          'catalogue_id')

        hotels_with_ispromoted = cls._update_hotels_with_promotion_priority(hotels, sales_poc_email)
        return hotels_with_ispromoted

    @classmethod
    def _update_hotels_with_promotion_priority(cls, hotels, sales_poc_email):
        try:
            hotel_ids = [hotel['hotel_id'] for hotel in hotels]
            promoted_hotel_ids = B2BHotelService.filter_promoted_hotels(hotel_ids, sales_poc_email=sales_poc_email)
            state_attribs = cls._get_attribute_dict(constants.HotelAttributes.LEGAL_STATE_CODE)
            brand_attribs = cls._get_attribute_dict(constants.HotelAttributes.BRAND_TYPE)
            legal_state_code_name = cls._get_legal_state_code_name()
            hotels_with_is_promoted = []
            for hotel in hotels:
                legal_state_code = state_attribs.get(hotel['id'])
                state_name = legal_state_code_name.get(legal_state_code)
                is_promoted = False
                brand_type = brand_attribs.get(hotel['id'])

                if hotel['hotel_id'] in promoted_hotel_ids:
                    is_promoted = True
                hotels_with_is_promoted.append({
                    'id': hotel['hotel_id'],
                    'name': hotel['name'],
                    'city': hotel['city'],
                    'state_name': state_name,
                    'is_promoted': is_promoted,
                    'external_id': hotel['external_id'],
                    'brand_type': brand_type,
                    'catalogue_id': hotel['catalogue_id']
                })
        except Exception as e:
            logger.info("Could not get saleable with is_promotion {err}".format(err=str(e)))
            raise
        return hotels_with_is_promoted

    @classmethod
    def _get_attribute_dict(cls, key):
        attribs = HotelAttribute.objects.filter(key=key).values('hotel_id', 'value')
        return {att['hotel_id']: att['value'] for att in attribs}

    @classmethod
    def get_external_hotel_ids(cls, hotel_ids):
        hotels = Hotel.objects.filter(id__in=hotel_ids).values('external_id')
        return [hotel['external_id'] for hotel in hotels]

    @classmethod
    def _get_legal_state_code_name(cls):
        states = State.objects.values('legal_state_code', 'name')
        return {s['legal_state_code']: s['name'] for s in states}

    @classmethod
    def get_internal_hotel_ids(cls, external_hotel_ids):
        hotels = Hotel.objects.filter(external_id__in=external_hotel_ids,
                                      catalogue_id__isnull=False).values('id', 'external_id')
        external_ids_to_b2b_internal_ids_map = {hotel['external_id']: hotel['id'] for hotel in hotels}
        if not external_ids_to_b2b_internal_ids_map:
            msg = "Hotels {h} does not exist".format(h=external_ids_to_b2b_internal_ids_map)
            raise HotelsDoesNotExists(msg)
        return list(external_ids_to_b2b_internal_ids_map.values())

    @classmethod
    def get_internal_hotel_ids_for_catalogue_ids(cls, catalogue_ids):
        hotels = Hotel.objects.filter(catalogue_id__in=catalogue_ids).values('id', 'catalogue_id')
        catalogue_ids_to_b2b_internal_ids_map = {hotel['catalogue_id']: hotel['id'] for hotel in hotels}
        if not catalogue_ids_to_b2b_internal_ids_map:
            msg = "Hotels {h} does not exist".format(h=catalogue_ids_to_b2b_internal_ids_map)
            raise HotelsDoesNotExists(msg)
        return list(catalogue_ids_to_b2b_internal_ids_map.values())
