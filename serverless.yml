service: joker

# Layer Management:
# - Using pre-built layer from aws.js config instead of auto-generating on each deployment
# - To update layer after requirements change:
#   1. Set pythonRequirements.layer to true
#   2. Uncomment custom and plugins sections below
#   3. Deploy to generate new layer version
#   4. Update layer ARN in serverless/config/<stage>/aws.js
#   5. Set pythonRequirements.layer back to false

frameworkVersion: '>=2.67.0 <3.0.0'
variablesResolutionMode: 20210326

provider:
  name: aws
  runtime: python3.11
  stage: ${opt:stage, 'staging'}  # default to 'staging' unless overridden
  region: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):region}
  memorySize: 128
  timeout: 120
  logRetentionInDays: 30
  deploymentPrefix: joker-artifacts
  tags:
    project: joker
    environment: ${opt:stage, self:provider.stage}
  environment:
    SENTRY_DSN: ${self:custom.secrets.SENTRY_DSN}
    INTERFACE_EXCHANGE_BACKEND_URL: ${file(serverless/config/${opt:stage, self:provider.stage}/urls.js):INTERFACE_EXCHANGE_BACKEND_URL}
    SERVICE_NAME: joker
    APP_ENV: ${opt:stage, self:provider.stage}
    CLUSTER_IDENTIFIER: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):cluster_identifier}
    AWS_SECRET_PREFIX: 'apps/joker'
  vpc:
    securityGroupIds: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):securityGroups}
    subnetIds: ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):subnets}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - secretsmanager:GetSecretValue
            - secretsmanager:DescribeSecret
          Resource: arn:aws:secretsmanager:${self:provider.region}:${aws:accountId}:secret:*

package:
  patterns:
    - '!**'
    - 'app/**'
    - '!app/**/__pycache__/**'

functions:
  jokerHandler:
    handler: app.main_handler.handler
    layers:
      # - ${file(serverless/config/${opt:stage, self:provider.stage}/aws.js):jokerRequirementsLayer}
      - { Ref: PythonRequirementsLambdaLayer } # Uncomment to use auto-generated layer
    events:
      - sns: joker-events-topic
      - http:
          path: /api/v1/
          method: ANY

custom:
  pythonRequirements:
    dockerizePip: true
    usePoetry: false
    slim: true
    useStaticCache: false
    useDownloadCache: false
    layer: true  # Disabled auto layer creation - using pre-built layer instead
    # Set to true when you need to rebuild the layer after requirements change

  secretsFilePathPrefix: serverless
  secrets: ${file(serverless/secrets.${opt:stage, self:provider.stage}.yml)}

plugins:
  - serverless-python-requirements
  - serverless-secrets-plugin
