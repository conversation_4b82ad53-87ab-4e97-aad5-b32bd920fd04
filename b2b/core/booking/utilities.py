"""
All variables in this file booking means it is an instance of the data class Booking
"""
from decimal import Decimal

from cached_property import cached_property

from core.hotel.services import GetHotel
from core.pricing.data_classes import TaxRequest
from core.pricing.services.get_tax import GetTax


class _Price(object):
    """
    this will serve as a holder to provide a concrete contract to the callers where
    room_charges and addon_charges are used.
    """

    def __init__(self, pre_tax, tax):
        self.pre_tax = Decimal(pre_tax)
        self.tax = Decimal(tax)


class BookingTotals(object):

    def __init__(self, booking):
        self.booking = booking
        self._hotel = None

    @property
    def hotel(self):
        if not self._hotel:
            self._hotel = GetHotel(hotel_id=self.booking.hotel_id).get_hotel()
        return self._hotel

    @cached_property
    def room_charges(self):
        for room_stay in self.booking.room_stays:
            if not room_stay.price.is_tax_populated():
                room_stay.price = self.get_price_with_tax(room_stay.price, room_stay.type.sku)

        active_room_stays = [rs.price.total for rs in self.booking.room_stays if rs.is_active]
        if not active_room_stays:
            return _Price(pre_tax=0, tax=0)
        total_room_charge = sum(active_room_stays)
        return _Price(pre_tax=total_room_charge.pre_tax, tax=total_room_charge.tax)

    @cached_property
    def addon_charges(self):
        for addon in self.booking.addons:
            if not addon.price.is_tax_populated():
                addon.price_per_quantity = self.get_price_with_tax(addon.price_per_quantity, addon.type.sku)

        active_addons = [ad.price_per_quantity.total * ad.quantity for ad in self.booking.addons if ad.is_active]
        if not active_addons:
            return _Price(pre_tax=0, tax=0)
        total_addon_charge = sum(active_addons)
        return _Price(pre_tax=total_addon_charge.pre_tax, tax=total_addon_charge.tax)

    @property
    def total_paid(self):
        if not self.booking.payments:
            return 0
        return sum([payment.amount for payment in self.booking.payments])

    @cached_property
    def _total_amount_tuple(self):
        estimated_total_pre_tax_amount = self.room_charges.pre_tax + self.addon_charges.pre_tax
        estimated_total_tax_amount = self.room_charges.tax + self.addon_charges.tax
        return estimated_total_pre_tax_amount, estimated_total_tax_amount

    @property
    def total_pretax(self):
        return self._total_amount_tuple[0]

    @property
    def total_tax(self):
        return self._total_amount_tuple[1]

    @property
    def total_posttax(self):
        return self.total_pretax + self.total_tax

    @property
    def total_credit(self):
        if self.booking.payment_type.is_credit_type:
            return self.total_pretax + self.total_tax
        return 0

    @property
    def total_balance(self):
        return self.total_posttax - self.total_paid - self.total_credit

    def get_price_with_tax(self, price, sku):
        tax_request = TaxRequest(hotel=self.hotel, price=price, sku_category=sku)
        price_with_tax = GetTax(tax_request).get_tax()
        return price_with_tax
