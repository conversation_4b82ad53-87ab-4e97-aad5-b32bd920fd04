# -*- coding: utf-8 -*-
# pylint: disable=invalid-name
from b2b import constants
from common.data_classes.base import BaseDataClass


class Status(BaseDataClass):

    def __init__(self, name, priority, old_name):
        self.name = name
        self.old_name = old_name
        self.priority = priority

    def __gt__(self, other):
        return self.priority > other.priority

    def __hash__(self):
        return hash(self.name)

    def __ge__(self, other):
        return self.priority >= other.priority

    def __lt__(self, other):
        return self.priority < other.priority

    def __le__(self, other):
        return self.priority <= other.priority

    def __eq__(self, other):
        return self.priority == other.priority

    def __repr__(self):
        return "<{kls}:{n}>".format(kls=self.__class__.__name__,
                                    n=self.name,
                                    )

    def __str__(self):
        return self.name


InitiatedStatus = Status('initiated', 1, constants.Booking.INITIATED)
ReservedStatus = Status('reserved', 2, constants.Booking.SAVED)
ConfirmedStatus = Status('confirmed', 3, constants.Booking.CONFIRMED)
PartCheckinStatus = Status('part_checked_in', 4, constants.Booking.CONFIRMED)
CheckinStatus = Status('checked_in', 5, constants.Booking.CONFIRMED)
PartCheckoutStatus = Status('part_checked_out', 6, constants.Booking.CONFIRMED)
CheckoutStatus = Status('checked_out', 7, constants.Booking.CONFIRMED)
CancelledStatus = Status('cancelled', 8, constants.Booking.CANCELLED)
NoShowStatus = Status('noshow', 9, constants.Booking.CANCELLED)

AllStatuses = {
    InitiatedStatus,
    ReservedStatus,
    ConfirmedStatus,
    PartCheckinStatus,
    CheckinStatus,
    PartCheckoutStatus,
    CheckoutStatus,
    CancelledStatus,
    NoShowStatus,
}

InactiveStatuses = {CancelledStatus, NoShowStatus}

DisabledStatuses = {CheckoutStatus}

ActiveStatuses = AllStatuses - InactiveStatuses


def status_from_value(value):
    try:
        # this check is put for HX bookings in DB, that are in intermediate cancellation state.
        # Need to be removed when HX dependency goes away
        if value == constants.Booking.INIT_CANCEL:
            return CancelledStatus

        if str(value).lower() == 'temporary':
            # this is for crs when they moved from `reserved` to `temporary`
            return ReservedStatus

        statuses = [status for status in AllStatuses
                  if str(value).lower() in (status.name.lower(), status.old_name.lower())]

        # ordering by priority since if value is `confirmed` it will match ConfirmedStatus, PartCheckinStatus etc.
        # since AllStatuses is a set it might give PartCheckinStatus for a Confirmed Booking. Will happen in Hx flow
        # where db get booking will get by old name
        statuses.sort(key=lambda status: status.priority)
        return statuses[0]
    except IndexError:
        raise RuntimeError('Invalid booking status: {}'.format(value))


def status_in_active_statuses(status):
    return status in ActiveStatuses


def status_in_disabled_statuses(status):
    return status in DisabledStatuses
