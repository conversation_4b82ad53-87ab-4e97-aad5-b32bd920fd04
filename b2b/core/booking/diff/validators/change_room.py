from core.booking.data_classes.status import PartCheckinStatus
from core.booking.diff import ChangeRoomDiff
from common.diff_and_patch.exceptions import InvalidDelta
from common.diff_and_patch.validator import Validator


@Validator.register
class ChangeRoomValidator(Validator):
    diff_item = ChangeRoomDiff

    def validate(self, delta):

        current_room_stay = delta.current_state
        new_room_stay = delta.new_state

        if not current_room_stay.is_active:
            raise InvalidDelta("Disabled rooms have been changed", errors=str(current_room_stay))

        for guest_stay in current_room_stay.guest_stays:
            try:
                current_guest_stay = current_room_stay.guest_stays[guest_stay.uid]
                new_guest_stay = new_room_stay.guest_stays[guest_stay.uid]

                if (current_guest_stay.status >= PartCheckinStatus
                        and new_guest_stay.checkin != current_guest_stay.checkin):
                    raise InvalidDelta("Cant change checkin when the room in already checked in",
                                       errors=[str(current_guest_stay)])

                if not current_guest_stay.is_active and new_guest_stay != current_guest_stay:
                    raise InvalidDelta("Cant change guest stay when it is not active", errors=[str(current_guest_stay)])

            except KeyError:
                pass
