# -*- coding: utf-8 -*-
from core.pricing.data_classes import Price
from core.pricing.interfaces.get_pricing import GetPricingInterface


class WebPricingScheme(GetPricingInterface):

    def get_prices(self, room_type=None):
        from b2b.domain.services import HotelService
        ta_pricing_applicable = self.legal_entity.is_web_pricing_for_ta_or_tmc_applicable()

        website_hotel_price = HotelService.get_hotel_price_details(
            self.hotel.uid,
            self.from_date.date(),
            self.to_date.date(),
            self.room_config.to_str(),
            ta_pricing_applicable
        )

        prices = {}

        for _room_type in self.hotel.room_types:

            if room_type and room_type != _room_type:
                continue  # populating a certain room type if it is present

            pre_tax, tax = 0, 0

            try:

                pre_tax, tax = self._get_website_price_for_particular_room_type(website_hotel_price, _room_type.name)

            except KeyError:
                pass  # room not given by web price

            price = Price.from_total_price(pre_tax, tax, self.from_date, self.to_date)
            prices[_room_type] = price

        return prices

    def _get_website_price_for_particular_room_type(self, website_hotel_price, room_type):

        pre_tax, tax = 0, 0

        if website_hotel_price[room_type.upper()]:
            web_price_details = website_hotel_price[room_type.upper()][0]
            if web_price_details['total_price_breakup']:
                total_price_breakup = web_price_details['total_price_breakup']
                if total_price_breakup['pre_tax']:
                    pre_price_breakup = total_price_breakup['pre_tax']
                    if pre_price_breakup['sell_price']:
                        pre_tax = pre_price_breakup['sell_price']

                if total_price_breakup['tax']:
                    web_tax_breakup = total_price_breakup['tax']
                    tax = web_tax_breakup['total_value']

        return pre_tax, tax
