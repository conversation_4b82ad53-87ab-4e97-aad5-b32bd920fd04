import abc

from core.hotel.data_classes import Hotel


class GetHotelInterface(object, metaclass=abc.ABCMeta):

    def __init__(self, hotel_id):
        self.hotel_id = hotel_id

    def get_hotel(self):
        hotel = self._get_hotel()
        assert isinstance(hotel, Hotel)
        return hotel

    @abc.abstractmethod
    def _get_hotel(self):
        pass

    @classmethod
    @abc.abstractmethod
    def from_cs_id(cls, hotel_cs_id):
        pass
