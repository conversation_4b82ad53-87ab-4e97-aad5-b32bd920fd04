# -*- coding: utf-8 -*-
from common.data_classes.base import BaseDataClass
from .addon_type import AllAddonTypes
from .room_type import RoomTypes


class Hotel(BaseDataClass):

    def __init__(self, uid, cs_id, name, address, room_types):
        self.uid = uid
        self.cs_id = cs_id
        self.name = name
        self.address = address
        self.phone_number = ''
        self.legal_state_code = ''
        self.is_located_in_sez = False  # stubbing this for now. Need to populate from CS

        if isinstance(room_types, RoomTypes):
            self.room_types = room_types
        else:
            self.room_types = RoomTypes(room_types)

    def available_addon_types(self):
        return AllAddonTypes

    def __repr__(self):
        return '{n} ({uid}, {cs_id})'.format(
            n=self.name,
            uid=self.uid,
            cs_id=self.cs_id,
        )

    def __str__(self):
        return str(self.name)

    def __hash__(self):
        return hash(self.name)
