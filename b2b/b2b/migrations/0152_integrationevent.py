# -*- coding: utf-8 -*-
# Generated by Django 1.10.7 on 2023-03-21 06:40
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import jsonfield.fields


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0151_auto_20230301_2113'),
    ]

    operations = [
        migrations.CreateModel(
            name='IntegrationEvent',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('event_id', models.Char<PERSON>ield(max_length=25, primary_key=True, serialize=False)),
                ('body', jsonfield.fields.JSONField()),
                ('event_type', models.CharField(max_length=25)),
                ('status', models.<PERSON>r<PERSON><PERSON>(default='unpublished', max_length=20)),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='generated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'IntegrationEvent',
                'verbose_name_plural': 'IntegrationEvents',
                'abstract': False,
                'default_permissions': ('add', 'change', 'read', 'delete'),
            },
        ),
    ]
