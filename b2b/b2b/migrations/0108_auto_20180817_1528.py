# -*- coding: utf-8 -*-
# pylint: disable-all
from django.db import migrations


def chunks(queryset, chunksize):
    """Yield successive chunk-sized chunks from queryset."""
    queryset = queryset.order_by('pk')
    for i in range(0, queryset.count(), chunksize):
        yield queryset.filter(pk__gt=i, pk__lte=i + chunksize)


def undo_set_default_crs_type(apps, schema_editor):
    Booking = apps.get_model("b2b", "Booking")
    qs = Booking.objects.all()
    bookings = chunks(qs, chunksize=10000)

    for chunk in bookings:
        chunk.update(crs_type=None)


def set_default_crs_type(apps, schema_editor):
    Booking = apps.get_model("b2b", "Booking")
    qs = Booking.objects.all()
    bookings = chunks(qs, chunksize=10000)

    for chunk in bookings:
        chunk.update(crs_type='HX')


class Migration(migrations.Migration):
    dependencies = [
        ('b2b', '0107_auto_20180817_1447'),
    ]

    operations = [
        migrations.RunPython(set_default_crs_type, reverse_code=undo_set_default_crs_type),
    ]
