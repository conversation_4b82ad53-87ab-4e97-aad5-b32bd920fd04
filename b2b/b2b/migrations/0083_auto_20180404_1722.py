# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0082_merge'),
    ]

    operations = [
        migrations.AddField(
            model_name='auditlog',
            name='entity_id',
            field=models.Char<PERSON>ield(max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='entity_type',
            field=models.CharField(max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='user_email',
            field=models.CharField(max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='auditlog',
            name='booking',
            field=models.ForeignKey(to='b2b.Booking', null=True, on_delete=models.CASCADE),
        ),
    ]
