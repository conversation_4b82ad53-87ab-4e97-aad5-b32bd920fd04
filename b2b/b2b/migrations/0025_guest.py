# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0024_auto_20170320_2215'),
    ]

    operations = [
        migrations.CreateModel(
            name='Guest',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('salutation', models.CharField(default=b'Mr', max_length=10, null=True, blank=True, choices=[(b'Mr', b'Mr'), (b'Mrs', b'Mrs'), (b'Ms', b'Ms')])),
                ('name', models.CharField(max_length=250)),
                ('gender', models.Char<PERSON>ield(default=b'M', max_length=1, null=True, blank=True, choices=[(b'M', b'Male'), (b'F', b'Female')])),
                ('email', models.EmailField(max_length=250)),
                ('phone', models.CharField(max_length=50, null=True, blank=True)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
