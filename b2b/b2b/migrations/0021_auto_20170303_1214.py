# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0020_merge'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customroompricing',
            name='post_tax_custom_price',
            field=models.DecimalField(default=0, max_digits=20, decimal_places=2),
        ),
        migrations.AlterField(
            model_name='standardinclusionspricing',
            name='post_tax_price',
            field=models.DecimalField(default=0, max_digits=20, decimal_places=2),
        ),
        migrations.AlterField(
            model_name='standardroompricing',
            name='post_tax_default_price',
            field=models.DecimalField(default=0, max_digits=20, decimal_places=2),
        ),
        migrations.AlterField(
            model_name='standardroompricing',
            name='post_tax_floor_price',
            field=models.DecimalField(default=0, max_digits=20, decimal_places=2),
        ),
        migrations.AlterField(
            model_name='standardroompricing',
            name='post_tax_quote_price',
            field=models.DecimalField(default=0, max_digits=20, decimal_places=2),
        ),
    ]
