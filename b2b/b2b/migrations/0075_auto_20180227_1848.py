# -*- coding: utf-8 -*-
from django.contrib.contenttypes.models import ContentType
from django.db import migrations


def create_upload_permissions(apps, schema_editor):
    Permission = apps.get_model("auth", "Permission")
    standard_room_pricing = apps.get_model("b2b", "standardroompricing")
    standard_inclusion_pricing = apps.get_model("b2b", "standardinclusionspricing")

    content_type_standardroompricing = ContentType.objects.get_for_model(standard_room_pricing)
    content_type_standardinclusionspricing = ContentType.objects.get_for_model(standard_inclusion_pricing)

    Permission.objects.create(
        name='Can upload pricing csv for standard room',
        codename='upload_standardroompricing',
        content_type_id=content_type_standardroompricing.id,
    )
    Permission.objects.create(
        name='Can upload pricing csv for standard inclusions',
        codename='upload_standardinclusionspricing',
        content_type_id=content_type_standardinclusionspricing.id,
    )


class Migration(migrations.Migration):
    dependencies = [
        ('b2b', '0074_auto_20180213_1542'),
    ]

    operations = [
        migrations.RunPython(create_upload_permissions),
    ]
