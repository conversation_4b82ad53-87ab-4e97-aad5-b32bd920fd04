# -*- coding: utf-8 -*-
from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0080_merge'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='extra_id',
            field=models.<PERSON>r<PERSON>ield(default=None, max_length=20, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='payment',
            name='status',
            field=models.CharField(default=b'InitiatedOrder', max_length=50, choices=[(b'InitiatedOrder', b'InitiatedOrder'), (b'VerificationInitiated', b'VerificationInitiated'), (b'Verified', b'Verified'), (b'VerificationFailed', b'VerificationFailed'), (b'Refunded', b'Refunded')]),
        ),
    ]
