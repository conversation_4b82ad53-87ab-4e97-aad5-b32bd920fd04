# pylint:disable=too-many-locals
import calendar
import logging
from time import strftime

import htmlmin
from django.conf import settings
from django.template import loader, Context

from b2b import constants
from b2b.consumer.notif import get_notification_backend
from b2b.domain.utils import count_room_nights
from b2b.dto import EmailDTO
from b2b.models import Booking

logger = logging.getLogger(__name__)


class BookingReminderHandler(object):
    def get_reminder_context(self, booking, rooms_list):

        unconfirmed_rooms, confirmed_rooms = self.customise_unconfirmed_confirmed_rooms(rooms_list)
        all_guests = [guest for guest in booking.guests]

        try:
            from b2b.domain.services import BookingService

            legal_entity = BookingService.get_legal_entity_for_mail(booking)
            billing_address = legal_entity.get('address')

            hotel_name, hotel_address, hotel_phone_number, hotel_state_name = BookingService.get_booking_hotel_details(
                booking)

            context = dict(
                corporate=legal_entity.get('name'),
                gstin=legal_entity.get('gstin'),
                gstin_address=billing_address,
                corporate_street=",".join((billing_address['building'],
                                           billing_address['street'], billing_address['locality'])),
                corporate_city=billing_address['city'],
                corporate_state=billing_address['state'],
                corporate_zipcode=billing_address['pincode'],
                hotel_name=hotel_name,
                address=hotel_address,
                phone_number=hotel_phone_number,
                image_url='',
                booking_code=booking.booking_id,
                booking_date=str(booking.created_timestamp.date()),
                checkin=str(strftime('%d %b, %Y ', booking.checkin.date().timetuple())),
                checkin_day=calendar.day_name[booking.checkin.date().weekday()],
                checkout=str(strftime('%d %b, %Y ', booking.checkout.date().timetuple())),
                checkout_day=calendar.day_name[booking.checkout.date().weekday()],
                days=count_room_nights(str(booking.checkin.date()), str(booking.checkout.date())),
                bookingStatus="Confirmed",
                pah=0 if booking.payment_type.name == constants.Booking.BTC else 1,
                booking_type=booking.payment_type.name,
                # Need to ask what exactly to put
                total_guests=len(all_guests),
                guest_without_detail=BookingService.guest_without_details(thscBooking=booking),
                expiry_date=str(strftime('%d %b, %Y ', booking.checkin.date().timetuple())),
                unconfirmed_rooms=unconfirmed_rooms,
                confirmed_rooms=confirmed_rooms,
                checkout_time=constants.DEFAULT_CHECKOUT_TIME,
                has_d5_rate_plan=booking.is_ta
            )


        except Exception as e:
            msg = "Error building context while sending '{type}' confirmation email for booking '{bid}': {err}"
            logger.info(msg.format(type=type, bid=booking.booking_id, err=str(e)))
            raise

        return context

    # pylint:disable=R0201
    def get_reminder_email_dto(self, booking, context):

        booker_email = booking.created_by.email if booking.created_by is not None else '<EMAIL>'
        from b2b.domain.services import BookingService
        final_guest_emails = BookingService.get_not_empty_emails(thscBooking=booking)

        final_guest_emails = [email for email in final_guest_emails if email]

        if not final_guest_emails:
            final_guest_emails = ['<EMAIL>']

        # in case this is not prod system, prepend the subject with environment name
        email_subject = "Gentle Reminder - {hotel} - {bid}".format(hotel=booking.hotel.name,
                                                                   bid=booking.booking_id)
        # build the email dto
        email_tmpl_config = settings.NOTIFICATION_CONF['email_templates']['booking']['reminder']
        email_html = loader.render_to_string(template_name=email_tmpl_config['template'], context=context)

        # in case it's a non-prod environment, let the subject say which
        # pylint:disable=len-as-condition
        if settings.ENV_NAME != 'prod' or len(settings.ENV_NAME.strip()) == 0:
            email_subject = "[{e}] {s}".format(e=settings.ENV_NAME, s=email_subject)

        try:
            htmlmin.minify(email_html, remove_empty_space=True)
        except IndexError as e:
            raise Exception('Context has changed but template has not been changed due to {exc}'.format(exc=e))

        treebo_poc_email = []
        if booking.booker_company.account_treebo_poc:
            treebo_poc_email = [booking.booker_company.account_treebo_poc.email]

        email = EmailDTO(data=dict(
            sender=email_tmpl_config['sender'],
            to_list=email_tmpl_config['to'] + [booker_email] + final_guest_emails,
            cc_list=email_tmpl_config['cc'] + treebo_poc_email + booking.additional_notification_emails(),
            subject=email_subject,
            content=email_html,
            sender_name=email_tmpl_config['sender_name']
        ))

        return email

    # pylint:disable=R0201
    def send_reminder_email(self, booking, email_dto):

        notif_backend = get_notification_backend(settings.NOTIFICATION_CONF['backend'])

        try:
            # the backend needs to return a tracking id on success
            tracking_id = notif_backend.send_email(email_dto)

        except Exception as e:
            # todo: we need to catch specific exceptions here
            logger.info("Error notifying booking reminder for %s: %s", booking.booking_id, str(e))
            raise

        return tracking_id

    # pylint:disable=R0201
    def create_reminder_booking_attribute(self, booking, booking_attr, tracking_id):
        booking = Booking.objects.get(booking_id=booking.booking_id)
        booking.bookingattributes_set.create(key=booking_attr,
                                             value=tracking_id)
        msg = "saved '{type}' email tracking id '{tid}' to booking-attribs for booking '{bid}'"
        logger.info(msg.format(type='booking_reminder', tid=tracking_id, bid=booking.booking_id))

    # pylint:disable=R0201
    def customise_unconfirmed_confirmed_rooms(self, rooms_list):

        unconfirmed_rooms = []
        confirmed_rooms = []
        for room in rooms_list:
            empty_guest_count = 0
            is_room_with_empty_guest = False
            if room.get('all_guests_stays'):
                for guest_stay in room['all_guests_stays']:
                    if guest_stay.guest.name == 'empty' or guest_stay.guest.name == '' or \
                            guest_stay.guest.name == 'N/A' or guest_stay.guest.name == 'guest':
                        empty_guest_count += 1
                        is_room_with_empty_guest = True
                if is_room_with_empty_guest:
                    if room.get('primary_guest'):
                        room['primary_guest'].name = str(
                            empty_guest_count) + ' Guest' if empty_guest_count == 1 else str(
                            empty_guest_count) + ' Guests'
                        unconfirmed_rooms.append(room)
                else:
                    confirmed_rooms.append(room)

        return unconfirmed_rooms, confirmed_rooms

    def notify(self, booking):

        rooms_list = [dict(room_type=record.name,
                           adults=len(record.active_guest_stays),
                           primary_guest=record.active_guest_stays[0].guest,
                           all_guests_stays=record.active_guest_stays)
                      for record in booking.room_stays]

        context = self.get_reminder_context(booking, rooms_list)
        email_dto = self.get_reminder_email_dto(booking, context)
        tracking_id = self.send_reminder_email(booking, email_dto)
        self.create_reminder_booking_attribute(booking,
                                               constants.BookingAttributes.BOOKING_REMINDER_MAIL,
                                               tracking_id)
