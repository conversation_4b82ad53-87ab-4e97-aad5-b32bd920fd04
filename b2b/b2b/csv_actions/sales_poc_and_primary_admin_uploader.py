from csv_uploader.handler import CsvHandlerRegisty
from csv_uploader.models import CsvJobItem
from csv_uploader.validator import CsvValidatorRegistry
from django.contrib.auth import get_user_model

from b2b.constants import B2BEventType, IntegrationEventStatus
from b2b.csv_actions.utils.common_utils import generate_event_id
from b2b.dto.integration_event import IntegrationEventDTO

from b2b.admin import CustomPermissionsMixin
from b2b.models import Corporate
from b2b.models.integration_event import IntegrationEvent
from b2b.models import Account, AccountLegalEntity
from b2b.utils.legal_entity_utils import create_legal_entity_dto

UploaderName = 'spoc_and_admin_uploader'  # pylint: disable=invalid-name

# pylint: disable=invalid-name
UploaderHeaders = [
    'account_id',
    'sales_poc_email',
    'primary_admin_email',
]

User = get_user_model()


# pylint: disable=unused-argument
@CsvHandlerRegisty.sync_handler(UploaderName)
def handler(row, csv_job_item_id):
    account = Account.objects.select_related('treebo_poc',
                                             'primary_admin').get(account_id=row['account_id'].strip().lower())
    uploaded_by = CsvJobItem.objects.select_related('csv_job__uploaded_by').get(
        id=csv_job_item_id).csv_job.uploaded_by
    account.updated_by = uploaded_by
    sales_poc_email = row['sales_poc_email'].strip()
    primary_admin_email = row['primary_admin_email'].strip()

    account_updated = False
    if sales_poc_email and account.treebo_poc.email.strip().lower() != sales_poc_email.lower():
        account_updated = True
        sales_poc = User.objects.get(email__iexact=sales_poc_email)
        account.treebo_poc = sales_poc

    if primary_admin_email and account.primary_admin.email.strip().lower() != primary_admin_email.lower():
        account_updated = True
        primary_admin = User.objects.get(email__iexact=primary_admin_email)
        account.primary_admin = primary_admin

    if not account_updated:
        return

    account.save()

    account_legal_entity = AccountLegalEntity.objects.filter(account=account, is_default=True).first()
    if not account_legal_entity:
        return
    event_dto = IntegrationEventDTO(
        data=dict(
            event_id=generate_event_id(),
            event_type=B2BEventType.LEGAL_ENTITY_UPDATED,
            body=create_legal_entity_dto(account_legal_entity.legal_entity, account),
            status=IntegrationEventStatus.UNPUBLISHED,
            generated_by_id=uploaded_by.id,
        )
    )

    if event_dto.is_valid():
        IntegrationEvent.objects.create(**event_dto.data)


@CsvValidatorRegistry.header_validator(UploaderName, UploaderHeaders)
def header_validator(user, header):
    if not CustomPermissionsMixin.has_upload_permission(user, Corporate):
        raise RuntimeError('Permission Denied')
    return header == UploaderHeaders


# pylint: disable=unused-argument
@CsvValidatorRegistry.row_validator(UploaderName)
def row_validator(user, row):
    return bool(row['account_id']) and (bool(row['sales_poc_email']) or bool(['primary_admin_email']))
