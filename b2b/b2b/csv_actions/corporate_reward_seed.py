import json
import math
from treebo_commons.utils import dateutils

import requests
from csv_uploader.handler import CsvHandlerRegisty
from csv_uploader.validator import CsvValidatorRegistry
from django.conf import settings

from b2b.admin import CustomPermissionsMixin
from b2b.models import Account
from loyalty.models import LoyaltyPoints
import logging

logger = logging.getLogger(__name__)

UploaderName = 'corporate_reward_seed'
UploaderHeaders = [
    'account_id',
    'amount',
    'amount_type',
    'expiry_days',
    'remarks',
    'hotel_crs_id'
]


def get_api_url():
    return settings.B2B_HOST + '/loyalty/loyalty-manage/'


def get_data(data):
    current_quarter = int(math.ceil(float(dateutils.current_date().month) / 3))
    seed_data = dict()

    charge = dict(amount=float(data.get('amount')), amount_type=data.get('amount_type'),
                  expiry_days=data.get('expiry_days'),
                  ref_id=data.get('remarks'))
    tmp_data = dict()
    tmp_data['charge_details'] = [charge]
    tmp_data['quarter'] = current_quarter
    tmp_data['year'] = dateutils.current_date().year
    tmp_data['invoice_id'] = 'NULL'
    account = Account.objects.get(account_id=data.get('account_id'), is_active=True)
    seed_data['revenue_details'] = [tmp_data]
    seed_data['legal_entity_id'] = account.default_legal_entity().legal_entity_id
    seed_data['is_seeding'] = True
    seed_data['hotel_crs_id'] = data.get('hotel_crs_id')

    return seed_data


def make_client_call(request_data):
    response = requests.post(url=get_api_url(), data=json.dumps(request_data),
                             headers={"content-type": "application/json"})
    return response


@CsvHandlerRegisty.sync_handler(UploaderName)
def handler(row, csv_job_item_id):
    # retrieving data from csv row
    row['account_id'] = row['account_id'].strip()
    row['amount'] = row['amount'].strip().replace(',', '')
    row['amount_type'] = (row.get('amount_type') and row['amount_type'].strip()) or settings.LOYALTY['points_type']
    row['expiry_days'] = (row.get('expiry_days') and row['expiry_days'].strip()) or settings.LOYALTY['expire_limit']
    row['remarks'] = row.get('remarks') and row['remarks'].strip()
    row['hotel_crs_id'] = (row.get('hotel_crs_id') and row['hotel_crs_id'].strip()) or '1502702'
    try:
        request_data = get_data(row)
        response = make_client_call(request_data)
        if response.status_code == 200:
            logger.info("points given successfully. Data {data}".format(data=json.dumps(request_data)))
        else:
            logger.info("points given failed. Data {data}. Error {err}".format(data=json.dumps(request_data),
                                                                               err=response.reason))
            raise
    except Account.DoesNotExist as e:
        logger.info(f"Account with ID {row['account_id']} is in-active or does not exist.")
        raise
    except Exception as e:
        logger.info(
            "Getting error {error} while seeding TCR points for data {data}".format(error=e, data=row))
        raise


@CsvValidatorRegistry.header_validator(UploaderName, UploaderHeaders)
def header_validator(user, header):
    if not CustomPermissionsMixin.has_upload_permission(user, LoyaltyPoints):
        raise RuntimeError('Permission Denied')
    return header == UploaderHeaders


# pylint: disable=unused-argument
@CsvValidatorRegistry.row_validator(UploaderName)
def row_validator(user, row):
    return True
