[{"fields": {"phone_number": "", "city": "", "name": "treebo hotel01", "locality": "", "created_at": "2016-12-12T12:06:31.612Z", "modified_at": "2016-12-12T12:06:31.612Z", "hotel_id": "hotel01", "hotelogix_id": "ksljd1093kld", "street": "", "active": true}, "model": "b2b.hotel", "pk": 1}, {"fields": {"phone_number": "080 41471414", "city": "Bengaluru", "name": "Treebo Elmas", "locality": "Koramanga<PERSON>", "created_at": "2016-12-21T11:15:16.744Z", "modified_at": "2016-12-21T11:16:19.724Z", "hotel_id": "<PERSON><PERSON>", "hotelogix_id": "12792", "street": "412/A, 1st C cross, 7th Block, Koramangala", "active": true}, "model": "b2b.hotel", "pk": 2}, {"fields": {"phone_number": "080 41471414", "city": "Bengaluru", "name": "Treebo Elmas", "locality": "Koramanga<PERSON>", "created_at": "2016-12-21T11:15:16.744Z", "modified_at": "2016-12-21T11:16:19.724Z", "hotel_id": "1", "hotelogix_id": "12792", "external_id": "2", "street": "412/A, 1st C cross, 7th Block, Koramangala", "active": true}, "model": "b2b.hotel", "pk": 3}, {"fields": {"hotel": 2, "corporate": 1, "corp_hx_id": "c3GdTs4|"}, "model": "b2b.hxmapping", "pk": 1}, {"fields": {"phone_number": "", "first_name": "shreyas", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "is_active": false, "created_at": "2016-12-12T12:05:46.809Z", "modified_at": "2016-12-12T12:05:46.809Z", "is_superuser": false, "is_staff": false, "last_login": null, "groups": [], "user_permissions": [], "is_guest": false, "password": "", "email": "<EMAIL>"}, "model": "b2b.user", "pk": 1}, {"fields": {"phone_number": "", "first_name": "someone", "last_name": "somewhere", "is_active": false, "created_at": "2016-12-12T12:05:46.822Z", "modified_at": "2016-12-12T12:05:46.822Z", "is_superuser": false, "is_staff": false, "last_login": null, "groups": [], "user_permissions": [], "is_guest": false, "password": "", "email": "<EMAIL>"}, "model": "b2b.user", "pk": 2}, {"fields": {"name": "corp01 admin", "gender": "M", "created_at": "2016-12-12T12:05:46.827Z", "modified_at": "2016-12-12T12:05:46.827Z", "phone": null, "salutation": "Mr", "email": null}, "model": "b2b.person", "pk": 1}, {"fields": {"name": "primary guest", "gender": "M", "created_at": "2016-12-12T12:08:18.259Z", "modified_at": "2016-12-12T12:08:18.259Z", "phone": "438596893", "salutation": "Mr", "email": null}, "model": "b2b.person", "pk": 2}, {"fields": {"name": "his wife", "gender": "F", "created_at": "2016-12-12T12:14:08.269Z", "modified_at": "2016-12-12T12:14:08.269Z", "phone": null, "salutation": "Mrs", "email": null}, "model": "b2b.person", "pk": 4}, {"fields": {"name": "his son", "gender": "M", "created_at": "2016-12-12T12:16:27.873Z", "modified_at": "2016-12-12T12:16:27.874Z", "phone": null, "salutation": "Mr", "email": null}, "model": "b2b.person", "pk": 6}, {"fields": {"name": "another primary guest", "gender": "M", "created_at": "2016-12-12T12:16:27.886Z", "modified_at": "2016-12-12T12:16:27.886Z", "phone": null, "salutation": "Mr", "email": null}, "model": "b2b.person", "pk": 7}, {"fields": {"category": "Room Inclusions", "post_tax_price": "350.00", "inclusion": "ExtraPersonCharges", "created_at": "2016-12-12T13:18:09.088Z", "hotel": 1, "modified_at": "2016-12-12T13:18:09.089Z", "created_by": null, "from_date": null, "to_date": null, "pre_tax_price": "300.00", "pricing_mechanism": "PP"}, "model": "b2b.standardinclusionspricing", "pk": 1}, {"fields": {"category": "Room Inclusions", "post_tax_price": "350.00", "inclusion": "ExtraPersonCharges", "created_at": "2016-12-21T11:22:28.458Z", "hotel": 2, "modified_at": "2016-12-21T11:22:28.458Z", "created_by": null, "from_date": null, "to_date": null, "pre_tax_price": "300.00", "pricing_mechanism": "PP"}, "model": "b2b.standardinclusionspricing", "pk": 2}, {"fields": {"city": null, "max_length_of_stay": null, "post_tax_custom_price": "1950.00", "created_at": "2016-12-12T13:18:09.065Z", "hotel": 1, "modified_at": "2016-12-12T13:18:09.065Z", "created_by": null, "price_slab": "custom", "from_date": null, "to_date": null, "pre_tax_custom_price": "1650.00", "corporate": 1, "room_type": "Maple", "min_length_of_stay": 1}, "model": "b2b.customroompricing", "pk": 1}, {"fields": {"city": null, "max_length_of_stay": null, "post_tax_custom_price": "1950.00", "created_at": "2016-12-21T11:22:28.445Z", "hotel": 2, "modified_at": "2016-12-21T11:22:28.445Z", "created_by": null, "price_slab": "custom", "from_date": null, "to_date": null, "pre_tax_custom_price": "1650.00", "corporate": 1, "room_type": "Maple", "min_length_of_stay": 1}, "model": "b2b.customroompricing", "pk": 2}, {"fields": {"pre_tax_floor_price": "500.00", "description": null, "created_at": "2016-12-12T13:17:26.883Z", "hotel": 1, "modified_at": "2016-12-12T13:17:26.883Z", "post_tax_default_price": "1200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "1800.00", "pre_tax_default_price": "1000.00", "to_date": null, "post_tax_floor_price": "600.00", "room_type": "Acacia", "pre_tax_quote_price": "1500.00"}, "model": "b2b.standardroompricing", "pk": 1}, {"fields": {"pre_tax_floor_price": "1500.00", "description": null, "created_at": "2016-12-12T13:17:46.314Z", "hotel": 1, "modified_at": "2016-12-12T13:17:46.314Z", "post_tax_default_price": "2200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "2800.00", "pre_tax_default_price": "2000.00", "to_date": null, "post_tax_floor_price": "1600.00", "room_type": "Maple", "pre_tax_quote_price": "2500.00"}, "model": "b2b.standardroompricing", "pk": 2}, {"fields": {"pre_tax_floor_price": "2500.00", "description": null, "created_at": "2016-12-12T13:17:46.324Z", "hotel": 1, "modified_at": "2016-12-12T13:17:46.324Z", "post_tax_default_price": "3200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "3800.00", "pre_tax_default_price": "3000.00", "to_date": null, "post_tax_floor_price": "2600.00", "room_type": "Oak", "pre_tax_quote_price": "3500.00"}, "model": "b2b.standardroompricing", "pk": 3}, {"fields": {"pre_tax_floor_price": "3500.00", "description": null, "created_at": "2016-12-12T13:17:46.331Z", "hotel": 1, "modified_at": "2016-12-12T13:17:46.331Z", "post_tax_default_price": "4200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "4800.00", "pre_tax_default_price": "4000.00", "to_date": null, "post_tax_floor_price": "3600.00", "room_type": "Mahogany", "pre_tax_quote_price": "4500.00"}, "model": "b2b.standardroompricing", "pk": 4}, {"fields": {"pre_tax_floor_price": "500.00", "description": null, "created_at": "2016-12-21T11:22:28.411Z", "hotel": 2, "modified_at": "2016-12-21T11:22:28.411Z", "post_tax_default_price": "1200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "1800.00", "pre_tax_default_price": "1000.00", "to_date": null, "post_tax_floor_price": "600.00", "room_type": "Acacia", "pre_tax_quote_price": "1500.00"}, "model": "b2b.standardroompricing", "pk": 5}, {"fields": {"pre_tax_floor_price": "1500.00", "description": null, "created_at": "2016-12-21T11:22:28.425Z", "hotel": 2, "modified_at": "2016-12-21T11:22:28.425Z", "post_tax_default_price": "2200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "2800.00", "pre_tax_default_price": "2000.00", "to_date": null, "post_tax_floor_price": "1600.00", "room_type": "Maple", "pre_tax_quote_price": "2500.00"}, "model": "b2b.standardroompricing", "pk": 6}, {"fields": {"pre_tax_floor_price": "2500.00", "description": null, "created_at": "2016-12-21T11:22:28.433Z", "hotel": 2, "modified_at": "2016-12-21T11:22:28.433Z", "post_tax_default_price": "3200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "3800.00", "pre_tax_default_price": "3000.00", "to_date": null, "post_tax_floor_price": "2600.00", "room_type": "Oak", "pre_tax_quote_price": "3500.00"}, "model": "b2b.standardroompricing", "pk": 7}, {"fields": {"pre_tax_floor_price": "3500.00", "description": null, "created_at": "2016-12-21T11:22:28.440Z", "hotel": 2, "modified_at": "2016-12-21T11:22:28.440Z", "post_tax_default_price": "4200.00", "created_by": null, "from_date": null, "post_tax_quote_price": "4800.00", "pre_tax_default_price": "4000.00", "to_date": null, "post_tax_floor_price": "3600.00", "room_type": "Mahogany", "pre_tax_quote_price": "4500.00"}, "model": "b2b.standardroompricing", "pk": 8}, {"fields": {"sector": "", "slab_type": "default", "tmc_commission_percentage": "0.00", "city": null, "meal_plan": "MAP", "zipcode": null, "created_by": 1, "state": null, "email": null, "treebo_poc": 1, "parent": "", "mop": "Direct", "is_tmc": false, "phone": null, "corporate_id": "corp01", "address": null, "active": true, "legal_name": "corporate number one", "created_at": "2016-12-12T12:05:46.895Z", "modified_at": "2016-12-12T12:05:46.916Z", "trading_name": "the corporate"}, "model": "b2b.corporate", "pk": 1}, {"fields": {"auth_user": "1", "corporate": "1", "created_at": "2016-12-12T12:05:46.895Z", "modified_at": "2016-12-12T12:05:46.916Z"}, "model": "b2b.corpadmin", "pk": 1}, {"fields": {"booking_id": "TRB-8548662557", "check_in": "2017-04-21", "check_out": "2017-04-22", "tmc_commission": "0.00", "channel": "website", "agent": "1", "corporate": "1", "hotel": "1", "created_at": "2016-12-12T12:05:46.895Z", "modified_at": "2016-12-12T12:05:46.916Z"}, "model": "b2b.booking", "pk": 1}, {"fields": {"room_type": "Oak", "pre_tax_price": "200", "post_tax_price": "400", "meal_plan": "MP", "meal_type": "BTC", "pre_tax_meal_charges": "50", "tmc_commission": "0.00", "booking": "1", "guest": "1", "post_tax_meal_charges": "60", "created_at": "2016-12-12T12:05:46.895Z", "modified_at": "2016-12-12T12:05:46.916Z"}, "model": "b2b.bookedroom", "pk": 1}]