from rest_framework import serializers

from b2b.dto.person import PersonDTO
from b2b.models.corporate import Corporate


class CorporateDTO(serializers.ModelSerializer):
    relationship_manager = PersonDTO(read_only=True, source='treebo_poc')
    name = serializers.CharField(source='trading_name')

    class Meta:
        model = Corporate
        fields = ['relationship_manager', 'name', 'corporate_id']
