from decimal import Decimal

from rest_framework import serializers

from b2b.constants import CorporateAttributes
from b2b.models import Corporate, User, Address


class DynamicFieldsSerializerMixin(serializers.ModelSerializer):

    def __init__(self, *args, **kwargs):
        fields = kwargs.pop('fields', None)
        super(DynamicFieldsSerializerMixin, self).__init__(*args, **kwargs)

        if fields is not None:
            allowed = set(fields)
            existing = set(self.fields.keys())
            for field_name in existing - allowed:
                self.fields.pop(field_name)


class _AddressDTO(serializers.ModelSerializer):
    zip_code = serializers.CharField(source='pincode', required=False)

    class Meta:
        model = Address
        fields = ['building', 'street', 'locality', 'landmark', 'city', 'state', 'zip_code', 'country']


class _UserDTO(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'phone_number']


class CorporateDetailsDTO(DynamicFieldsSerializerMixin):
    created_by = serializers.SerializerMethodField()
    finance_admin = serializers.SerializerMethodField()
    primary_admin = serializers.SerializerMethodField()
    treebo_poc = serializers.SerializerMethodField()
    billing_address = serializers.SerializerMethodField()
    contact_address = serializers.SerializerMethodField()
    inside_sales_poc = serializers.SerializerMethodField()
    virtual_account_number = serializers.SerializerMethodField()
    pan_number = serializers.SerializerMethodField()
    amount_received = serializers.SerializerMethodField()
    cin_number = serializers.SerializerMethodField()
    gstin_number = serializers.SerializerMethodField()
    credit_limit = serializers.SerializerMethodField()
    billing_period = serializers.SerializerMethodField()
    stay_summary_aggregation_criteria = serializers.SerializerMethodField()
    is_ta = serializers.SerializerMethodField()
    navision_code = serializers.SerializerMethodField()
    legal_name = serializers.SerializerMethodField()
    booking_request_attachment_reminder_enabled = serializers.SerializerMethodField()
    booking_request_attachment_reminder_period = serializers.SerializerMethodField()
    stay_summary_booking_request_attachment_enabled = serializers.SerializerMethodField()

    def get_created_by(self, instance):
        return _UserDTO(instance.created_by).data if instance.created_by else {}

    def get_finance_admin(self, instance):
        return _UserDTO(instance.finance_admin).data if instance.finance_admin else {}

    def get_primary_admin(self, instance):
        return _UserDTO(instance.primary_admin).data if instance.primary_admin else {}

    def get_treebo_poc(self, instance):
        return _UserDTO(instance.treebo_poc).data if instance.treebo_poc and _UserDTO(instance.treebo_poc).data else {}

    def get_billing_address(self, instance):
        billing_address = instance.default_billing_address
        return _AddressDTO(billing_address).data if billing_address and _AddressDTO(
            billing_address).data else {}

    def get_contact_address(self, instance):
        return _AddressDTO(instance.contact_address).data if instance.contact_address and _AddressDTO(
            instance.contact_address).data else {}

    def get_inside_sales_poc(self, instance):
        return _UserDTO(instance.inside_sales_poc).data if instance.inside_sales_poc and _UserDTO(
            instance.inside_sales_poc).data else {}

    def get_virtual_account_number(self, instance):
        return instance.get_attr(CorporateAttributes.VIRTUAL_ACCOUNT_NUMBER)

    def get_pan_number(self, instance):
        return instance.get_attr(CorporateAttributes.PAN_NUMBER)

    def get_amount_received(self, instance):
        return Decimal(instance.get_attr(CorporateAttributes.AMOUNT_RECEIVED)) if instance.get_attr(
            CorporateAttributes.AMOUNT_RECEIVED) else 0.0

    def get_cin_number(self, instance):
        return instance.get_attr(CorporateAttributes.CIN_NUMBER)

    def get_gstin_number(self, instance):
        return instance.default_gstin

    def get_credit_limit(self, instance):
        return Decimal(instance.get_attr(CorporateAttributes.CREDIT_LIMIT)) if instance.get_attr(
            CorporateAttributes.CREDIT_LIMIT) else 0.0

    def get_billing_period(self, instance):
        return instance.get_attr(CorporateAttributes.BILLING_PERIOD)

    def get_stay_summary_aggregation_criteria(self, instance):
        aggregation_criteria = instance.get_attr(CorporateAttributes.STAY_SUMMARY_AGGREGATION_CRITERIA)
        if not aggregation_criteria:
            return None
        return aggregation_criteria

    def get_booking_request_attachment_reminder_period(self, instance):
        booking_request_attachment_reminder_period = instance.get_attr(
            CorporateAttributes.BOOKING_REQUEST_ATTACHMENT_REMINDER_PERIOD)
        return int(booking_request_attachment_reminder_period) if booking_request_attachment_reminder_period else None

    def get_booking_request_attachment_reminder_enabled(self, instance):
        booking_request_attachment_reminder_enabled = instance.get_attr(
            CorporateAttributes.BOOKING_REQUEST_ATTACHMENT_REMINDER_ENABLED)
        if not booking_request_attachment_reminder_enabled:
            return False
        return True if int(booking_request_attachment_reminder_enabled) else False

    def get_stay_summary_booking_request_attachment_enabled(self, instance):
        stay_summary_booking_request_attachment_enabled = instance.get_attr(
            CorporateAttributes.STAY_SUMMARY_BOOKING_REQUEST_ATTACHMENT_ENABLED)
        if not stay_summary_booking_request_attachment_enabled:
            return False
        return True if int(stay_summary_booking_request_attachment_enabled) else False

    def get_is_ta(self, instance):
        return instance.is_ta()

    def get_navision_code(self, instance):
        return instance.get_attr(CorporateAttributes.NAVISION_CODE)

    def get_legal_name(self, instance):
        return instance.default_legal_name

    def validate(self, data):
        """
        Check that billing_address and legal_name fields are not empty

        :param data:
        """
        if not data['legal_name']:
            raise serializers.ValidationError(
                "Legal name does not exists for corporate with id {c}".format(c=data['corporate_id']))
        if not data['billing_address']:
            raise serializers.ValidationError(
                "Billing Address does not exists for corporate with id {c}".format(c=data['corporate_id']))
        return data

    class Meta:
        model = Corporate
        fields = ['corporate_id', 'legal_name', 'trading_name', 'email', 'phone', 'treebo_poc',
                  'slab_type', 'is_tmc', 'tmc_commission_percentage', 'btc_enabled', 'active', 'parent',
                  'created_by', 'credit_period', 'finance_admin', 'is_ta', 'navision_code',
                  'primary_admin', 'billing_address', 'contact_address', 'inside_sales_poc', 'virtual_account_number',
                  'pan_number', 'amount_received', 'cin_number', 'gstin_number', 'credit_limit', 'billing_period',
                  'stay_summary_aggregation_criteria', 'booking_request_attachment_reminder_enabled',
                  'booking_request_attachment_reminder_period', 'stay_summary_booking_request_attachment_enabled']
