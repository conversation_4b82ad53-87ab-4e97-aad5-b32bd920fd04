from rest_framework import serializers
from b2b.constants import HotelAttributes as HotelAttributesConstants
from b2b.models import Hotel
from b2b.models import HotelAttribute


class HotelInfoDTO(serializers.ModelSerializer):
    gstin_code = serializers.SerializerMethodField()

    def get_gstin_code(self, obj):
        hotel_attribute = HotelAttribute.objects.get(key=HotelAttributesConstants.GSTIN_CODE, hotel_id=obj.id)
        return hotel_attribute.value

    class Meta:
        model = Hotel
        fields = ['hotel_id', 'name', 'hotelogix_id', 'locality', 'street',
                  'city', 'phone_number', 'active', 'external_id', 'website', 'luxury_tax_code', 'email',
                  'address', 'service_tax_code', 'state_id', 'gstin_code']
