# -*- coding: utf-8 -*-
from django.conf import settings
from rest_framework import serializers
from common.rounded_decimal_field import RoundedDecimal<PERSON>ield


from b2b import constants


class _LegalEntityDataDTO(serializers.Serializer):
    legal_entity_id = serializers.CharField(max_length=200, allow_null=True, allow_blank=True)
    trading_name = serializers.CharField(max_length=200, allow_null=True, allow_blank=True)
    is_ta = serializers.BooleanField(default=False)


class BookingStatusDTO(serializers.Serializer):
    booking_id = serializers.CharField(max_length=30)
    booking_amount = RoundedDecimalField(max_digits=20, decimal_places=2, default=0)
    room_charges = RoundedDecimalField(max_digits=20, decimal_places=2, default=0)
    meal_charges = RoundedDecimalField(max_digits=20, decimal_places=2, default=0)
    inclusion_charges = RoundedDecimalField(max_digits=20, decimal_places=2, default=0)
    taxes = RoundedDecimalField(max_digits=20, decimal_places=2, default=0)
    status = serializers.ChoiceField(choices=constants.Booking.STATUSES)
    check_in = serializers.DateField(format=settings.BOOKING['date_format'])
    check_out = serializers.DateField(format=settings.BOOKING['date_format'])
    channel = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    sub_channel = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    legal_entity = _LegalEntityDataDTO(required=True, allow_null=True)
    is_soft_booking = serializers.BooleanField(default=False)
    agent_emails = serializers.ListField(default=[])

    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass
