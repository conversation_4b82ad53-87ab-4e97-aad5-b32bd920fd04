from decimal import Decimal

from rest_framework import serializers

from b2b.constants import CorporateAttributes, Corporates
from b2b.models import LegalEntity, User, Address
from b2b.models.corporate import CorporateLegalEntity, LegalEntityAttributes
from b2b.models.corporate.corporate_attributes import CorporateAttributes as corporate_attrib


class DynamicFieldsSerializerMixin(serializers.ModelSerializer):

    def __init__(self, *args, **kwargs):
        fields = kwargs.pop('fields', None)
        super(DynamicFieldsSerializerMixin, self).__init__(*args, **kwargs)

        if fields is not None:
            allowed = set(fields)
            existing = set(self.fields.keys())
            for field_name in existing - allowed:
                self.fields.pop(field_name)


class _AddressDTO(serializers.ModelSerializer):
    zip_code = serializers.CharField(source='pincode', required=False)

    class Meta:
        model = Address
        fields = ['building', 'street', 'locality', 'landmark', 'city', 'state', 'zip_code', 'country']


class UserDTO(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'phone_number']


class LegalEntityDetailsDTO(DynamicFieldsSerializerMixin):
    tan_number = serializers.SerializerMethodField()
    pan_number = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    phone_number = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    credit_limit = serializers.SerializerMethodField()
    credit_period = serializers.SerializerMethodField()
    billing_period = serializers.SerializerMethodField()
    stay_summary_aggregation_criteria = serializers.SerializerMethodField()
    booking_request_attachment_reminder_enabled = serializers.SerializerMethodField()
    booking_request_attachment_reminder_period = serializers.SerializerMethodField()
    gstin_number = serializers.SerializerMethodField()
    trading_name = serializers.SerializerMethodField()
    legal_entity_id = serializers.CharField()
    legal_name = serializers.SerializerMethodField()
    treebo_poc = serializers.SerializerMethodField()
    inside_sales_poc = serializers.SerializerMethodField()
    primary_admin = serializers.SerializerMethodField()
    primary_finance_admin = serializers.SerializerMethodField()
    hotel_finance_poc = serializers.SerializerMethodField()
    agency_type = serializers.SerializerMethodField()
    sector = serializers.SerializerMethodField()
    payment_source = serializers.SerializerMethodField()
    loyalty_enabled = serializers.SerializerMethodField()
    tac_commission = serializers.SerializerMethodField()
    ta_pricing_discount = serializers.SerializerMethodField()
    loyalty_level = serializers.SerializerMethodField()
    account_id = serializers.SerializerMethodField()

    def get_account_id(self, instance):
        account = instance.account
        return account and account.account_id

    def get_loyalty_level(self, instance):
        return instance.get_attr(Corporates.LOYALTY_LEVEL)

    def get_ta_pricing_discount(self, instance):
        return instance.get_attr(CorporateAttributes.TA_PRICING_DISCOUNT)

    def get_tac_commission(self, instance):
        return instance.get_attr(CorporateAttributes.TAC_COMMISSION)

    def get_loyalty_enabled(self, instance):
        return instance.get_attr(CorporateAttributes.LOYALTY_ENABLED)

    def get_payment_source(self, instance):
        return instance.get_attr(CorporateAttributes.PaymentSource.KEY)

    def get_agency_type(self, instance):
        return instance.get_attr(CorporateAttributes.AgencyTypes.KEY)

    def get_sector(self, instance):
        return instance.corporate.sector

    def get_credit_limit(self, instance):
        return Decimal(instance.get_attr(CorporateAttributes.CREDIT_LIMIT) or 0)

    def get_credit_period(self, instance):
        return instance.get_attr(CorporateAttributes.CREDIT_PERIOD) or '0'

    def get_billing_period(self, instance):
        return instance.get_attr(CorporateAttributes.BILLING_PERIOD) or '0'

    def get_legal_name(self, instance):
        return instance.name

    def get_trading_name(self, instance):
        corporate_details = self.context['legal_entity_id_to_corporate_map'][instance.id]
        return corporate_details.get('trading_name')

    def get_address(self, instance):
        address = instance.gstin_address_dict or instance.address
        return _AddressDTO(address).data if address and _AddressDTO(
            address).data else {}

    def get_treebo_poc(self, instance):
        return UserDTO(instance.account_treebo_poc).data if instance.account_treebo_poc else {}

    def get_inside_sales_poc(self, instance):
        return UserDTO(instance.account_inside_sales_poc).data if instance.account_inside_sales_poc else {}

    def get_hotel_finance_poc(self, instance):
        return UserDTO(instance.account_hotel_finance_poc).data if instance.account_hotel_finance_poc else {}
    
    def get_gstin_number(self, instance):
        return instance.gstin() and instance.gstin().gstin

    def get_tan_number(self, instance):
        corporate_details = self.context['legal_entity_id_to_corporate_map'][instance.id]
        return corporate_details.get('tan_number')

    def get_pan_number(self, instance):
        corporate_details = self.context['legal_entity_id_to_corporate_map'][instance.id]
        return corporate_details.get('pan_number')

    def get_email(self, instance):
        corporate_details = self.context['legal_entity_id_to_corporate_map'][instance.id]
        return corporate_details['email']

    def get_phone_number(self, instance):
        corporate_details = self.context['legal_entity_id_to_corporate_map'][instance.id]
        return corporate_details['phone_number']

    def get_primary_admin(self, instance):
        return UserDTO(instance.primary_admin).data if instance.primary_admin else {}

    def get_primary_finance_admin(self, instance):
        return UserDTO(instance.finance_admin).data if instance.finance_admin else {}

    def get_stay_summary_aggregation_criteria(self, instance):
        aggregation_criteria = instance.get_attr(CorporateAttributes.STAY_SUMMARY_AGGREGATION_CRITERIA)
        if not aggregation_criteria:
            return None
        return aggregation_criteria

    def get_booking_request_attachment_reminder_period(self, instance):
        booking_request_attachment_reminder_period = instance.get_attr(
            CorporateAttributes.BOOKING_REQUEST_ATTACHMENT_REMINDER_PERIOD)
        return int(booking_request_attachment_reminder_period) if booking_request_attachment_reminder_period else None

    def get_booking_request_attachment_reminder_enabled(self, instance):
        booking_request_attachment_reminder_enabled = instance.get_attr(
            CorporateAttributes.BOOKING_REQUEST_ATTACHMENT_REMINDER_ENABLED)
        if not booking_request_attachment_reminder_enabled:
            return False
        return True if int(booking_request_attachment_reminder_enabled) else False

    def get_stay_summary_booking_request_attachment_enabled(self, instance):
        stay_summary_booking_request_attachment_enabled = instance.get_attr(
            CorporateAttributes.STAY_SUMMARY_BOOKING_REQUEST_ATTACHMENT_ENABLED)
        if not stay_summary_booking_request_attachment_enabled:
            return False
        return True if int(stay_summary_booking_request_attachment_enabled) else False

    class Meta:
        model = LegalEntity
        fields = ['legal_name', 'legal_entity_id', 'trading_name', 'gstin_number', 'credit_limit',
                  'credit_period', 'billing_period', 'address', 'phone_number', 'email',
                  'pan_number', 'tan_number', 'slab_type', 'is_tmc', 'btc_enabled', 'active',
                  'btc_admin_approval', 'show_web_prices', 'posttax_enabled',
                  'stay_summary_aggregation_criteria', 'booking_request_attachment_reminder_enabled',
                  'booking_request_attachment_reminder_period', 'treebo_poc', 'inside_sales_poc',
                  'primary_admin', 'primary_finance_admin', 'agency_type', 'sector',
                  'payment_source', 'loyalty_enabled', 'tac_commission', 'ta_pricing_discount',
                  'loyalty_level', 'account_id', 'hotel_finance_poc']


class LegalEntityDetailsDTOV2(LegalEntityDetailsDTO):
    corporate_id = serializers.SerializerMethodField()

    def get_corporate_id(self, instance):
        return instance.corporate.corporate_id

    class Meta:
        model = LegalEntity
        fields = ['legal_name', 'legal_entity_id', 'trading_name', 'gstin_number', 'credit_limit',
                  'credit_period', 'billing_period', 'address', 'phone_number', 'email',
                  'pan_number', 'tan_number', 'slab_type', 'is_tmc', 'btc_enabled', 'active',
                  'btc_admin_approval', 'show_web_prices', 'posttax_enabled',
                  'stay_summary_aggregation_criteria', 'booking_request_attachment_reminder_enabled',
                  'booking_request_attachment_reminder_period', 'treebo_poc', 'inside_sales_poc',
                  'primary_admin', 'primary_finance_admin', 'corporate_id', 'agency_type', 'sector',
                  'payment_source', 'loyalty_enabled', 'tac_commission', 'ta_pricing_discount',
                  'loyalty_level', 'hotel_finance_poc']
