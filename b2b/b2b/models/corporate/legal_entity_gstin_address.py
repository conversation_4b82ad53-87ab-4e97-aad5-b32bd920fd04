# -*- coding: utf-8 -*-
from b2b.models.mixins import DefaultPermissions
from django.db import models
from djutil.models import TimeStampedModel
from .gstin import Gstin
from b2b.models import Address


class LegalEntityGstinAddress(TimeStampedModel, DefaultPermissions):
    """
    Mapping for Gstin for legal entities and address. One gstin can have multiple addresses but reserve is not true.
    """
    gstin = models.ForeignKey(Gstin, on_delete=models.CASCADE)
    address = models.ForeignKey(Address, on_delete=models.CASCADE)
    default = models.BooleanField(default=False)

    class Meta(DefaultPermissions.Meta):
        unique_together = ('gstin', 'address')
        db_table = 'b2b_corporate_legal_entity_gstin_address'

    def __str__(self):
        return "{gstin} | {address}".format(gstin=self.gstin.gstin, address=self.address.city)
