# -*- coding: utf-8 -*-
from django.db import models

from b2b.models.promoted_hotels import PromotedHotel
from b2b.models.mixins import DefaultPermissions
from djutil.models import TimeStampedModel


class SalesPocPromotedHotelsMapping(TimeStampedModel, DefaultPermissions):
    # TODO: This is all over the places, sales poc is referenced by user id rather than its own separate identity
    sales_poc_email = models.CharField(max_length=100)
    hotel = models.ForeignKey(PromotedHotel, on_delete=models.CASCADE)
    active = models.BooleanField(default=True)

    class Meta(DefaultPermissions.Meta):
        unique_together = ('sales_poc_email', 'hotel',)
