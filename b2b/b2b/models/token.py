# -*- coding: utf-8 -*-
import logging

from django.core.exceptions import MultipleObjectsReturned, ObjectDoesNotExist
from django.db import models
from djutil.models import TimeStampedModel

from b2b.models.mixins import DefaultPermissions


class Token(TimeStampedModel, DefaultPermissions):
    """Any kind of token with a category, key and value
    """
    category = models.CharField(max_length=255)
    key = models.CharField(max_length=255)
    value = models.TextField(null=False)

    class Categories:
        REFRESH_TOKEN = "refresh_token"

    def __str__(self):
        return "{cat} | {k} :: {v}".format(cat=self.category, k=self.key, v=self.value)
