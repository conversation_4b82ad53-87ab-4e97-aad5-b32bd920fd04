# -*- coding: utf-8 -*-
from django.conf import settings
from django.db import models
from django.db.models import Sum
from djutil.models import TimeStampedModel

from b2b import constants
from b2b.models import Booking
from b2b.models import Guest
from b2b.models.mixins import DefaultPermissions


class BookedRoom(TimeStampedModel, DefaultPermissions):
    """
    one booking => many booked rooms (potentially)
    """
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE)
    room_type = models.CharField(choices=constants.Rooms.TYPES,
                                 max_length=50)

    # primary guest
    # any additional guests will be entered in the AdditionalGuests table
    guest = models.ForeignKey(Guest, on_delete=models.CASCADE)

    pre_tax_price = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    post_tax_price = models.DecimalField(max_digits=20, decimal_places=2, default=0)

    meal_plan = models.CharField(choices=constants.Meal.PLANS,
                                 default=constants.Meal.DEFAULTS['plan'],
                                 max_length=50)
    meal_type = models.CharField(choices=constants.Meal.TYPES,
                                 default=constants.Meal.DEFAULTS['type'],
                                 null=True, blank=True, max_length=50)

    # todo: should this be in inclusions instead of here?
    pre_tax_meal_charges = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    post_tax_meal_charges = models.DecimalField(max_digits=20, decimal_places=2, default=0)

    tmc_commission = models.DecimalField(max_digits=20, decimal_places=2, default=0)

    class Meta(DefaultPermissions.Meta):
        pass

    def __str__(self):
        return f"{self.room_type} - {self.booking.booking_id}"

    def __unicode__(self):
        return self.__str__()

    def additional_guests(self):
        """
        looks up the AdditionalGuests table for guests related to this booked room
        and returns the number of additional guests

        this number can then be used to calculate extra-person charges
        :return: number of additional guests
        """
        return [ag.guest for ag in self.additionalguests_set.filter(booking_id=self.booking)]

    def guest_count(self):
        return len(self.additional_guests()) + 1

    def total_pre_tax_room_charges(self):
        """
        total = room_pre_tax + extra_person_charges - tmc_commission
        :return: total pre-tax room charges (float)
        """
        return round(float(self.pre_tax_price)
                     # + self.pre_tax_meal_charges
                     + self.extra_person_charges()
                     - float(self.tmc_commission), 2)

    def extra_person_charges(self, post_tax=False):
        """
        find out exra-person-charges associated with this room
        :param post_tax: should the post-tax charges be returned instead of pre-tax?
        :return: extra person charges applicable to this room
        """
        col_name = 'post_tax_price' if post_tax else 'pre_tax_price'

        const_ep_charges = constants.Inclusions.EPCharges
        ep_sum = self.bookinginclusions_set.filter(category=constants.Inclusions.CategoryMapping[const_ep_charges],
                                                   inclusion=const_ep_charges).aggregate(Sum(col_name))
        return float(ep_sum[col_name + '__sum'] or 0)

    def max_allowed_guests(self):
        """
        currently just returns a fixed value from app setting
        will eventually consult the Rooms model for deviations
        :return: max number of allowed guests per room
        """
        # todo: add Rooms model, and consult it for any deviations
        return settings.BOOKING['max_allowed_guests_per_room']

    def get_room_config(self):
        # Assuming get_all_guests_count returns adult counts only
        # '0' here represents child count
        return str(self.guest_count()) + '-' + '0'

    def all_guests_details(self):
        """
        :return: list of all guests in this room (model instances)
        """
        return [self.guest] + self.additional_guests()

    def get_addons(self):
        """
        :return: Booking Inclusions instance
        """
        return self.bookinginclusions_set.exclude(inclusion=constants.Inclusions.EPCharges)

    @property
    def adults(self):
        """
        :return: Count of all adults for now returns the same as guest_count
        """
        return self.guest_count()

    @property
    def children(self):
        """
        :return: Returns zero for now
        """
        return 0
