import logging

from dateutil.parser import parse

from b2b import constants
from b2b.models.booking import Booking
from b2b.orchestrator.dtos.crs_event_dto import CRSEventDTO
from b2b.payments.models.payment import Payment
from b2b.payments.services.payments import PaymentService
from common.utils.slack import send_slack_notif

logger = logging.getLogger(__name__)


class ProcessCRSEvent(object):

    def consume_event(self, crs_event_data):
        """
        :param crs_event_data:
        :return: tuple("success_status", "message")
        """
        try:
            dto = CRSEventDTO(data=crs_event_data)
            if not dto.is_valid():
                message = "Invalid event data format, error: {err}".format(err=dto.errors)
                logger.warning(message)
                return False, message
            crs_event = dto.data
            for event in crs_event['events']:
                if event['entity_name'] == 'bill':

                    self._injest_bill_event_and_add_b2b_payments(event['payload'])
                elif event['entity_name'] == 'booking':
                    self._update_b2b_booking(event['payload'])
            return True, "Consumed"
        except Exception as e:
            msg = "Error in consuming CRS Event: {event}, error: {err}".format(event=crs_event_data, err=str(e))
            logger.info(msg)
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)

    def _injest_bill_event_and_add_b2b_payments(self, payload):
        logger.info('Processing Bill Event: {d}'.format(d=payload))
        crs_booking_id = payload['parent_reference_number']
        net_paid_amount = float(payload['net_paid_amount'])
        total_pretax_amount = float(payload['total_pretax_amount'])
        total_tax_amount = float(payload['total_tax_amount'])
        try:
            booking = Booking.objects.get(crs_id=crs_booking_id)
            booking.pre_tax_price = total_pretax_amount
            booking.tax = total_tax_amount
            booking.save()

            total_payments_in_b2b = PaymentService.get_total_paid(booking_id=booking.booking_id)
            if abs(net_paid_amount - total_payments_in_b2b) >= 1:
                difference = round(net_paid_amount - total_payments_in_b2b, 2)
                order_id = PaymentService.generate_unique_order_id()
                payment_data = dict(
                    order_id=order_id,
                    booking_id=booking.booking_id,
                    amount=difference,
                    currency='INR',
                    status=constants.Payments.VERIFIED,
                    payment_source=Payment.PaymentSource.CRS
                )
                PaymentService.add_payment(payment_data)
                logger.info("Registering a payment from CRS, {payment_data}".format(payment_data=payment_data))

        except Booking.DoesNotExist:
            message = "Booking ID with crs_id: {crs_id} is not present in database".format(crs_id=crs_booking_id)
            logger.info(message)
        except Exception as e:
            message = "Error while processing payload: {payload}, error: {err}".format(payload=payload, err=str(e))
            logger.info(message)

    def _update_b2b_booking(self, payload):
        logger.info('Processing Booking Event: {d}'.format(d=payload))
        crs_booking_id = payload['booking_id']
        checkin_date = parse(payload['checkin_date'])
        checkout_date = parse(payload['checkout_date'])
        room_night_count = self._count_room_nights(payload['room_stays'])
        try:
            booking = Booking.objects.get(crs_id=crs_booking_id)
            booking.check_in = checkin_date
            booking.check_out = checkout_date
            booking.room_night_count = room_night_count
            if payload['status'].lower() == constants.Booking.CANCELLED.lower():
                booking.status = constants.Booking.CANCELLED
            booking.save()
        except Booking.DoesNotExist:
            logger.info("Booking with crs_id: {crs_id} is not present in database".format(crs_id=crs_booking_id))
        except Exception as e:
            message = "Error while processing payload: {payload}, error: {err}".format(payload=payload, err=str(e))
            logger.info(message)

    def _count_room_nights(self, room_stays):
        count = 0
        for _ in room_stays:
            if _['status'] != 'cancelled':
                checkin_date = parse(_['checkin_date']).date()
                checkout_date = parse(_['checkout_date']).date()
                days = (checkout_date - checkin_date).days
                count += days
        return count
