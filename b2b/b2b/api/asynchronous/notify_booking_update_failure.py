# -*- coding: utf-8 -*-
import logging

from b2b.api.asynchronous.api_responses import AsyncAPIResponse
from b2b.api.api import TreeboAP<PERSON>
from b2b.api.request_dtos import NotifyBookingUpdateFailureDTO
from b2b.domain.services import NotificationService

logger = logging.getLogger(__name__)

class NotifyBookingUpdateFailureAPI(TreeboAPI):
    """
    used for notifying failure to update a booking

    WATNING: THIS IS FOR INTERNAL-USE-ONLY AS OF NOW
    """
    def post(self, request):
        """
        given a booking-id we send the required failure notifications
        :param request: NotifyBookingUpdateFailureDTO
        """

        dto = NotifyBookingUpdateFailureDTO(data=request.data)
        if not dto.is_valid():
            errors = ["{k}: {v}".format(k=k, v=';'.join(dto.errors[k])) for k in dto.errors]
            return AsyncAPIResponse.unable_to_notify_booking_update_failure(booking_id='<missing>',
                                                                            errors=errors)

        parent_booking_id = dto.data['parent_booking_id']
        updated_booking_id = dto.data['updated_booking_id']

        try:
            NotificationService.booking_update_failed(parent_booking_id,
                                                      updated_booking_id,
                                                      errors=dto.data['errors'])

        except Exception as e:
            logger.info("error occurred while notifying "
                             "booking failure for {bid} : {err}".format(err=str(e),
                                                                        bid=parent_booking_id))
            return AsyncAPIResponse.unable_to_notify_booking_update_failure(booking_id=parent_booking_id,
                                                                            errors=[str(e)])

        response = AsyncAPIResponse.success_response(message="Notified booking update failure "
                                                             "for {bid}".format(bid=parent_booking_id))
        return response
