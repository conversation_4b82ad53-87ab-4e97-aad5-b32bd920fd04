import logging

from rest_framework import status
from rest_framework.response import Response

from b2b.api.api import TreeboAP<PERSON>
from b2b.api.request_dtos import EposRequestDTO
from b2b.domain.services import CorporateService
from b2b.domain.services.exceptions import InvalidDTO

logger = logging.getLogger(__name__)

class EposAPI(TreeboAPI):
    def get(self, request):
        # NOTE: Use-case is depreciated
        try:
            dto = EposRequestDTO(data=dict(booking_ids= request.query_params['booking_ids'].split(",")))
            if not dto.is_valid():
                raise InvalidDTO(dto)

            epos_response_dto = CorporateService.get_pos_enabled(dto)
            epos_response_dto.is_valid()
            return Response(data=epos_response_dto.data['pos_enabled_bookings'], status=status.HTTP_200_OK)
        except InvalidDTO as e:
            return Response(data={'errors': str(e)},
                            status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.info(
                "Error occurred while calling EposAPI api for bookings={query} with error {e}".format(
                    query=request.query_params, e=str(e)))
            return Response(data={'errors': str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

