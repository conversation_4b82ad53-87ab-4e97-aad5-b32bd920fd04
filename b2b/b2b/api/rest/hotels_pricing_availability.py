from b2b.dto import RoomPricingAvailabilityDTO
from b2b.domain.services import HotelService
from rest_framework.response import Response
import traceback
from b2b.api.api import TreeboAPI
import logging
from rest_framework import status


class HotelsPricingAvailabilityAPI(TreeboAPI):
    def get(self, request):
        logger = logging.getLogger(self.__class__.__name__)
        request_data = request.query_params
        try:
            availability_pricing_dto = RoomPricingAvailabilityDTO(data=request_data)
            if not availability_pricing_dto.is_valid():
                return Response(availability_pricing_dto.errors, status.HTTP_400_BAD_REQUEST)

            hotels_price_details = HotelService.get_hotel_room_price(availability_pricing_dto)

        except Exception as e:
            logger.info("error occurred while getting details for "
                         "hotel {hotel_id} checkin {checkin} checkout {checkout}"
                         "room_config {room_config} : {err}".format(err=str(e),
                                                                    hotel_id=request_data['hotel_ids'],
                                                                    checkin=request_data['checkin'],
                                                                    checkout=request_data['checkout'],
                                                                    room_config=request_data['roomconfig']))
            raise e
        return Response(data=hotels_price_details, status=200)
