import logging

from rest_framework import status
from rest_framework.response import Response

from b2b.api.api_error_response import APIErrorResponse
from b2b.api.rest.api import RestAPI
from b2b.domain.services import CorporateService
from b2b.domain.services.legal_entity import LegalEntityService

logger = logging.getLogger(__name__)


class LegalEntitiesAPI(RestAPI):

    def get(self, request):
        legal_entity_id = request.query_params.get('legal_entity_id')
        try:
            if legal_entity_id:
                legal_entities = LegalEntityService.get_sibling_legal_entities_by_id(legal_entity_id)
            else:
                legal_entities = LegalEntityService.get_sibling_legal_entities_by_user(request.user)

            legal_entities_response_dto = CorporateService.fetch_legal_entities_response_dto(legal_entities)

            return Response(data=legal_entities_response_dto.data, status=status.HTTP_200_OK)

        except Exception as err:
            logger.info("Fetching Legal Entities failed for user {user} - {message}".format(user=request.user.id,
                                                                                            message=str(err)))
            return APIErrorResponse.error_response(message="Failed to get sibling Legal Entities. Please try again",
                                                   resp_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

