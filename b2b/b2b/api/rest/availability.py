import logging
import traceback

from rest_framework import status
from rest_framework.response import Response

from b2b.api.api import TreeboAPI
from b2b.domain.services.availability import AvailabiltyService
from b2b.dto import CheckAvailabilityDTO

logger = logging.getLogger(__name__)


class AvailabilityAPI(TreeboAPI):
    def get(self, request):
        request_data = request.query_params

        try:
            availability_dto = CheckAvailabilityDTO(data=request.query_params)
            if not availability_dto.is_valid():
                return Response(availability_dto.errors, status.HTTP_400_BAD_REQUEST)

            response_data = AvailabiltyService.get_availability(availability_dto)

        except Exception as e:
            traceback.print_exc()
            logger.info("error occurred while getting details for "
                         "hotel {hotel_id} checkin {checkin} checkout {checkout}"
                         "room_config {room_config} : {err}".format(err=str(e),
                                                                    hotel_id=request_data['hotel'],
                                                                    checkin=request_data['check_in'],
                                                                    checkout=request_data['check_out'],
                                                                    room_config=request_data['room_config']))
            raise e

        response = Response(data=response_data, status=200)
        return response
