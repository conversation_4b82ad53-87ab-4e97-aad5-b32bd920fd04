# pylint: disable=invalid-name

import json
import logging

from requests import ConnectTimeout, HTTPError, ReadTimeout, Timeout, ConnectionError
from rest_framework import status
from rest_framework.response import Response

from b2b.api.api import TreeboAPI
from b2b.consumer.hotel_details import WebsiteHotelDetailsBackend
from b2b.dto import HotelDetailsDTO

logger = logging.getLogger(__name__)


class HotelDetailsAPI(TreeboAPI):
    def get(self, request, hotel_id):
        """
        ---
        parameters:
        - name: hotel_id
          description: Hotel Id
          required: true
          type: string
          paramType: path
        consumes:
        - application/json
        produces:
        - application/json
        """
        try:
            hotel_detail_dto = HotelDetailsDTO(data=dict(hotels=hotel_id))
            if not hotel_detail_dto.is_valid():
                return Response(hotel_detail_dto.errors, status.HTTP_400_BAD_REQUEST)
            response_data = json.loads(WebsiteHotelDetailsBackend.get_hotel_details_from_direct(hotel_detail_dto))
            return Response(response_data['data'])

        except (ConnectTimeout, HTTPError, ReadTimeout, Timeout, ConnectionError) as e:
            message = "HotelDetailFailure: Network error"
            logger.info(message)
            raise
        except Exception as e:
            message = "HotelDetailFailure: Error occured while loading hotel details for hotel_id {h_id}::{err}"
            logger.info(message.format(h_id=hotel_id, err=str(e)))
            raise
