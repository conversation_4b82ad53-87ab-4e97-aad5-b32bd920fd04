import logging

from rest_framework import status
from rest_framework.response import Response

from athena.services.booking import BookingService
from b2b.api import CsrfExemptSessionAuthentication
from b2b.api.api_error_response import APIErrorResponse
from b2b.api.dashboard.api import DashboardAP<PERSON>
from b2b.api.dashboard.authorization import DashboardAuthorization
from b2b.consumer.hotel_details.cataloging_service.catalog_service import CatalogService
from b2b.domain.services import HotelService
from b2b.domain.services.dashboard.v2.itinerary import ItineraryService
from b2b.domain.utils.hotel_id_conversion import map_external_ids
from b2b.dto import ItineraryRequestDTO
from b2b.models.corporate import Gstin

logger = logging.getLogger(__name__)


class ItineraryAPI(DashboardAPI):
    permission_classes = (DashboardAuthorization,)

    authentication_classes = (CsrfExemptSessionAuthentication,)

    def get(self, request):
        """
        ---
        serializer: b2b.dto.dashboard.ItineraryRequestDTO
        parameters:
        - name: checkin
          description: Check in date
          required: true
          type: string
          paramType: query
        - name: checkout
          description: Check out date
          required: true
          type: string
          paramType: query
        - name: roomconfig
          description: Room config. Ex - 1-0,2-1 means 2 rooms with 1 adult and no child in first room and 2 adults and
           1 child in second room
          required: true
          type: string
          paramType: query
        - name: hotel_id
          description: Hotel id
          required: true
          type: string
          paramType: query
        - name: legal_entity_id
          description: legal_entity_id
          required: true
          type: string
          paramType: query
        - name: roomtype
          description: Room type
          required: true
          type: string
          paramType: query
        consumes:
        - application/json
        produces:
        - application/json
        """
        request_data = request.query_params

        try:
            itinerary_request_dto = ItineraryRequestDTO(data=request.query_params)
            if not itinerary_request_dto.is_valid():
                return Response(itinerary_request_dto.errors, status.HTTP_400_BAD_REQUEST)

            # Creating new Itinerary DTO with b2b hotel id to pass it on to services
            itinerary_data = itinerary_request_dto.data

            itinerary_svc = ItineraryService(itinerary_data['legal_entity_id'], itinerary_data['roomconfig'],
                                             itinerary_data['checkin'], itinerary_data['checkout'])
            b2b_ids_ext_ids = map_external_ids(itinerary_data['hotel_id'].split(','))

            is_sez = has_provided_lut = False

            if itinerary_data['gstin']:
                gstin_details = Gstin.objects.filter(gstin=itinerary_data['gstin']).first()
                if gstin_details:
                    is_sez = gstin_details.is_sez
                    has_provided_lut = gstin_details.has_provided_lut

            cs_id = HotelService().cs_hotel_id_by_b2b_external_id(itinerary_data['hotel_id'])
            has_seller_provided_lut = CatalogService().has_seller_provided_lut(cs_id)

            itinerary_request_dto = ItineraryRequestDTO(data=dict(
                checkin=itinerary_data['checkin'],
                checkout=itinerary_data['checkout'],
                roomconfig=itinerary_data['roomconfig'],
                hotel_id=','.join(list(b2b_ids_ext_ids.keys())),
                legal_entity_id=itinerary_data['legal_entity_id'],
                roomtype=itinerary_data['roomtype'],
                gstin=itinerary_data['gstin'],
                seller_has_lut=has_seller_provided_lut,
                is_sez=is_sez,
                buyer_has_lut=has_provided_lut
            ))
            cheapest_room_pricing = itinerary_svc.get_itinerary_data(itinerary_request_dto)
            _blackout_start_date, _blackout_end_date = BookingService().get_blackout_date_range(
                from_date=itinerary_data['checkin'],
                to_date=itinerary_data['checkout'],
                b2b_hotel_id=str(','.join(list(b2b_ids_ext_ids.keys()))))

            # converting b2b hotel id to external hotel id
            cheapest_room_pricing['hotel']['id'] = b2b_ids_ext_ids[cheapest_room_pricing['hotel']['id']]
            cheapest_room_pricing['booking_gstin_enabled'] = True
            cheapest_room_pricing['blackout_date'] = _blackout_start_date
        except Exception as e:
            logger.info("error occurred while getting details for "
                             "hotel {hotel_id} checkin {checkin} checkout {checkout}"
                             "room_config {room_config} room type {room_type}: {err}".format(err=str(e),
                                                                                             hotel_id=request_data[
                                                                                                 'hotel_id'],
                                                                                             checkin=request_data[
                                                                                                 'checkin'],
                                                                                             checkout=request_data[
                                                                                                 'checkout'],
                                                                                             room_config=request_data[
                                                                                                 'roomconfig'],
                                                                                             room_type=request_data[
                                                                                                 'roomtype']))
            return APIErrorResponse.error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
        response = Response(data=cheapest_room_pricing, status=200)
        return response
