import logging
from collections import defaultdict

from django.conf import settings
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from rest_framework import status
from rest_framework.authentication import BasicAuthentication
from rest_framework.response import Response

from b2b.api import CsrfExemptSessionAuthentication
from b2b.api.api_error_response import APIErrorResponse
from b2b.api.dashboard.api import DashboardAPI
from b2b.api.dashboard.authorization import DashboardAuthorization
from b2b.domain.services.dashboard.services.get_booking_history import GetBookingHistory
from b2b.domain.services.exceptions import BookingsNotFound, InvalidDTO
from b2b.dto.dashboard import DashboardBookingHistoryDTO
from b2b.domain.services.booking import BookingService

logger = logging.getLogger(__name__)  # pylint: disable=invalid-name


class DashboardBookingHistoryAPI(DashboardAPI):
    permission_classes = (DashboardAuthorization,)

    authentication_classes = (CsrfExemptSessionAuthentication,)

    def get(self, request):
        try:
            legal_entity_id = request.query_params['legal_entity_id']
            upcoming_booking_page_number = int(request.query_params.get('upcomingBookingPageNumber', 1))
            previous_booking_page_number = int(request.query_params.get('previousBookingPageNumber', 1))
            records_per_page = int(request.query_params.get('totalRecordsPerPage', 5))

            booking_history = GetBookingHistory(legal_entity_id=legal_entity_id)

            upcoming_bookings_for_page = DashboardBookingHistoryAPI.get_bookings_by_page(
                booking_history.upcoming_bookings(),
                upcoming_booking_page_number,
                records_per_page,
            )

            previous_bookings_for_page = DashboardBookingHistoryAPI.get_bookings_by_page(
                booking_history.previous_bookings(),
                previous_booking_page_number,
                records_per_page,
            )

            booking_history_dto = DashboardBookingHistoryAPI._format_data(
                upcoming_bookings=upcoming_bookings_for_page,
                previous_bookings=previous_bookings_for_page,
                total_upcoming_bookings=booking_history.total_upcoming_bookings,
                total_previous_bookings=booking_history.total_previous_bookings,
            )

            return Response(data=booking_history_dto.data, status=status.HTTP_200_OK)

        except BookingsNotFound as e:
            return APIErrorResponse.error_response(str(e), status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.info("error {msg}".format(msg=str(e)))
            return APIErrorResponse.error_response(str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

    @staticmethod
    def get_bookings_by_page(bookings, page, records_per_page):
        paginator = Paginator(bookings, records_per_page)
        try:
            bookings = paginator.page(page)
        except PageNotAnInteger:
            # If page is not an integer, deliver first page.
            bookings = paginator.page(1)
        except EmptyPage:
            # If page is out of range (e.g. 9999), deliver last page of results.
            bookings = paginator.page(paginator.num_pages)
        return bookings

    @staticmethod
    def _format_data(upcoming_bookings, previous_bookings, total_upcoming_bookings, total_previous_bookings):
        upcoming_bookings_data = [DashboardBookingHistoryAPI._format_booking_data(booking)
                                  for booking in upcoming_bookings]
        previous_bookings_data = [DashboardBookingHistoryAPI._format_booking_data(booking)
                                  for booking in previous_bookings]

        data = {
            'total_upcoming_bookings': str(total_upcoming_bookings),
            'total_previous_bookings': str(total_previous_bookings),
            'upcoming': upcoming_bookings_data,
            'previous': previous_bookings_data,
        }

        bh_dto = DashboardBookingHistoryDTO(data={'bookings': data})
        if not bh_dto.is_valid():
            raise InvalidDTO(bh_dto)

        return bh_dto

    @staticmethod
    def _format_booking_data(booking):

        guest = booking.get_primary_guest_details()
        booked_rooms = booking.bookedroom_set.all()

        user_detail = {
            'name': guest.name,
            'phone': guest.phone,
            'email': guest.email,
        }

        hotel_detail = {
            'city': booking.hotel.city,
            'locality': booking.hotel.locality,
        }

        payment_detail = {
            'room_price': booking.room_charges(),
            'total': BookingService.total_booking_amount(booking),
            'tax': booking.tax,
        }

        room_config = {
            'room_type': booked_rooms[0].room_type,
            'no_of_adults': booking.get_guest_count(),
            'room_count': len(booked_rooms),
            'no_of_childs': 0,
        }

        room_count_per_room_type = defaultdict(int)
        for booked_room in booked_rooms:
            room_count_per_room_type[booked_room.room_type] += 1

        data = {
            'status': booking.status.upper(),
            'order_id': booking.booking_id,
            'checkin_date': booking.check_in,
            'checkout_date': booking.check_out,
            'sub_channel': booking.sub_channel,
            'booking_date': booking.created_at.strftime(settings.BOOKING['date_format']),
            'cancel_date': booking.get_attribute('cancel_date'),
            'user_detail': user_detail,
            'hotel_detail': hotel_detail,
            'paid_at_hotel': str(not booking.is_btc_booking()).title(),
            'payment_detail': payment_detail,
            'room_config': room_config,
            'hotel_name': booking.hotel.name,
            'audit': False,
            'room_config_string': ','.join([br.get_room_config() for br in booked_rooms]),
            'room_detail': ', '.join('{}: {}'.format(k, v) for k, v in list(room_count_per_room_type.items())),
            'special_preferences': booking.comments,
        }

        return data
