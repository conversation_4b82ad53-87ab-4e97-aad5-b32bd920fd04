import logging

from django.core.management import BaseCommand

from b2b.models import Corporate, User, Hotel, Guest, Person

logger = logging.getLogger('b2b')


class Command(BaseCommand):
    help = 'sanitizes db for dev setup'

    def handle(self, *args, **options):
        try:
            Corporate.objects.update(email='<EMAIL>', phone='9999999999')
            users = User.objects.exclude(groups__name__in=['b2b_developers'])
            for user in users:
                generate_email_id = 'b2b-test{id}@treebohotels.com'.format(id=user.id)
                user.email = generate_email_id
                user.phone_number = '9999999999'
                user.save()
            Person.objects.update(phone='9999999999')
            Hotel.objects.filter(id=2).update(catalogue_id='0001661')
            Guest.objects.update(email='<EMAIL>')
        except Exception as e:
            logger.info('Bulk update failed for dev setup due to {e}:'.format(e=e))
