# -*- coding: utf-8 -*-
import logging

from ths_common.utils.common_utils import safe_strip

from b2b.domain.pricing.exceptions import NoMatchesFound
from b2b.domain.utils import count_room_nights
from b2b.domain.utils.misc import get_multiplier_from_room_conf
from b2b.dto import EPChargeDTO
from .base import BaseInclusionsPricingStrategy


class IPSv2(BaseInclusionsPricingStrategy):
    def get_extra_person_charges(self, legal_entity, hotel, room_type, room_config, from_date, to_date,
                                 std_ep_charges, custom_ep_charges, *args, **kwargs):
        """
        NEW PRICING SCHEME
        determines applicable EP charges for given criteria

        no db calls. makes use of passed data (relevant charges from db) and model objects

        :param legal_entity: b2b.models.LegalEntity object
        :param hotel: b2b.models.Hotel object
        :param room_type: room type
        :param room_config: number of guests represented in '[num-adults]-[num-children]' format
        :param from_date: check-in
        :param to_date: check-out
        :param std_ep_charges: relevant standard extra-person charges from db
        :param custom_ep_charges: relevant custom extra-person charges from db
        :return: EPChargeDTO
        """
        logger = logging.getLogger(self.__class__.__name__)

        # get only rows specific to the hotel, and abiding by the new pricing scheme in case of standard pricing
        hotel_matches = []
        if custom_ep_charges:
            hotel_matches = [custom_price for custom_price in custom_ep_charges
                             if custom_price['hotel_id'] == hotel.id and custom_price['room_type'] == room_type]
            '''
            NOTE: As we are keeping custom extra person and room price both in same table hence putting this hack
            There might be scenario where latest custom room price record would not have extra person price defined
            '''
            if hotel_matches:
                hotel_matches = sorted(hotel_matches, key=lambda hotel_match: hotel_match['modified_at'], reverse=True)
                first_matched_custom_price = hotel_matches[0]
                if not first_matched_custom_price.get('ep_pre_tax_custom_price'):
                    hotel_matches = []

        if not hotel_matches and std_ep_charges:
            hotel_matches = [std_price for std_price in std_ep_charges
                             if std_price['hotel_id'] == hotel.id and std_price['new_pricing_scheme']]

        if not hotel_matches:
            raise NoMatchesFound(self.__class__.__name__)

        # works for upgraded/migrated databases
        price_matches = [hotel_match for hotel_match in hotel_matches if hotel_match['room_type'] == room_type]
        if not price_matches:
            # in case we didn't get any room specific matches, check if there are any records
            # that have hotel level extra person charges, i.e. room_type==''
            price_matches = [hotel_match for hotel_match in hotel_matches
                             if not hotel_match['room_type'] or safe_strip(hotel_match['room_type']) == '']

        if not price_matches:
            raise NoMatchesFound(self.__class__.__name__)

        # in case there are more than one matches, we choose the latest
        ep_charge = sorted(price_matches, key=lambda price_match: price_match['modified_at'], reverse=True)[0]

        ep_pre_tax_charge = ep_charge.get('ep_pre_tax_custom_price', ep_charge.get('pre_tax_price'))

        ep_charge_per_room_night = (float(ep_pre_tax_charge) * get_multiplier_from_room_conf(room_config))
        total_ep_charge = ep_charge_per_room_night * count_room_nights(from_date, to_date)

        ep_charges_dto = EPChargeDTO(data=dict(
            legal_entity_id=legal_entity.legal_entity_id,
            hotel=hotel.hotel_id,
            room_type=room_type,
            from_date=from_date,
            to_date=to_date,
            pre_tax_charge=ep_charge_per_room_night,  # this is per room night
            post_tax_charge=0,
            total_pre_tax_charge=total_ep_charge,  # this is total charge for the specified room-nights
            audit_log=["selection-criteria: standard-inclusion-pricing"]
        ))

        logger.debug("LegalEntity: {legal_entity_id}, hotel: {h}, room-type: {rt}, "
                     "pre_tax_charge_per_room_night: {pt}, "
                     "room-nights: {rn}, "
                     "total-pre-tax-charge: {tpt}".format(legal_entity_id=legal_entity.legal_entity_id, h=hotel,
                                                          rt=room_type,
                                                          pt=ep_charge_per_room_night,
                                                          rn=count_room_nights(from_date, to_date),
                                                          tpt=total_ep_charge))

        return ep_charges_dto
