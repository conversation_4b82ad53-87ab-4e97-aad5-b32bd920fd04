# -*- coding: utf-8 -*-
import logging

from b2b.constants import Rooms, Pricing
from b2b.domain.utils import count_room_nights
from b2b.dto import RoomPriceDTO
from .base_room_price_selector import BaseRoomPriceSelector


class DefaultRoomPriceSelector(BaseRoomPriceSelector):
    """
    this will always be the last selector. it will settle down with some default
    room prices as decided by the business.

    this is intended to cover for cases where the business missed configuring
    prices for some entities (some hotels or some rooms in the hotel etc)
    """

    def get_room_price(self, legal_entity, hotel, room_type, from_date, to_date, subchannel,
                       standard_prices, custom_prices, *args, **kwargs):
        logger = logging.getLogger(self.__class__.__name__)

        # todo: right now we are hard coding the values, but later we could get in some form of a
        #        hook into a db row, which the business can keep changing
        pre_tax = Rooms.DefaultRoomPrices[room_type]

        rp_data = dict(
            legal_entity_id=legal_entity.legal_entity_id,
            hotel=hotel.hotel_id,
            room_type=room_type,
            from_date=from_date,
            to_date=to_date,
            slab_type=Pricing.CUSTOM,
            pre_tax_price=pre_tax,
            post_tax_price=0,
            total_pre_tax_price=float(pre_tax) * count_room_nights(from_date, to_date),
            audit_log=['room: {r}, selector: {s}'.format(r=room_type, s=self.__class__.__name__)]
        )

        logger.warning("Falling back on default/fixed room prices ({r}: {p})".format(r=room_type,
                                                                                     p=pre_tax))

        logger.debug("Room-type: {rt}; "
                     "pre-tax-room-price: {pt}, "
                     "room-nights: {rn}, "
                     "total-pre-tax-price: {tpt}".format(rt=room_type,
                                                         pt=pre_tax,
                                                         rn=count_room_nights(from_date, to_date),
                                                         tpt=rp_data['total_pre_tax_price']))

        return RoomPriceDTO(data=rp_data)
