# -*- coding: utf-8 -*-
import logging

from b2b.domain.pricing.exceptions import NoMatchesFound, PriceNotAvailable
from b2b.domain.pricing.room_pricing_strategy.selectors import CorpHotelRoomTypeSelectorV2
from b2b.domain.pricing.room_pricing_strategy.selectors import DefaultRoomPriceSelector
from b2b.domain.pricing.room_pricing_strategy.selectors import HotelRoomTypeSelectorV2
from .base import BaseRoomPricingStrategy


class RPSv2(BaseRoomPricingStrategy):
    """
    room pricing strategy adhering to NEW PRICING SCHEME
    """

    def __init__(self):
        # instantiate price selectors related to this strategy
        self._selectors = [CorpHotelRoomTypeSelectorV2,
                           HotelRoomTypeSelectorV2,
                           DefaultRoomPriceSelector]

    def get_room_rate(self, legal_entity, hotel, room_type,
                      from_date, to_date, subchannel,
                      relevant_std_room_prices,
                      relevant_custom_room_prices,
                      *args, **kwargs):
        """
        consult different selectors and return the matching one
        :return: RoomPriceDTO
        """
        logger = logging.getLogger(self.__class__.__name__)

        # now check if there is any selector matching
        for selector in self._selectors:
            try:
                # if there is a match, return it
                return selector().get_room_price(legal_entity, hotel, room_type,
                                                 from_date, to_date, subchannel,
                                                 relevant_std_room_prices,
                                                 relevant_custom_room_prices,
                                                 *args, **kwargs)

            except NoMatchesFound as e:
                logger.info(str(e))

        # if we reach here, we don't have any matching room prices
        raise PriceNotAvailable(price_type='room')
