(function ($) {
  $(document).ready(function () {
    document.querySelector("#id_account").onchange = (element) => {
      console.log(element.target.value);

      $("#id_account_id").val(element.target.value);
      if (element.target.value == "") {
        $("#id_account_treebo_poc").val("");
        $("#id_account_primary_admin").val("");
        $("#id_account_inside_sales_poc").val("");
        $("#id_account_primary_finance_admin").val("");
        $("#id_account_hotel_finance_poc").val("");
        return;
      }
      $.get("/b2b/accounts/" + element.target.value + "/").success(function (
        response
      ) {
        console.log(response.data.treebo_poc.email);
        $("#id_account_treebo_poc").val(
          response.data.treebo_poc.name +
            " (" +
            response.data.treebo_poc.email +
            ")"
        );
        $("#id_account_primary_admin").val(
          response.data.primary_admin.name +
            " (" +
            response.data.primary_admin.email +
            ")"
        );
        $("#id_account_inside_sales_poc").val(
          response.data.inside_sales_poc.name +
            " (" +
            response.data.inside_sales_poc.email +
            ")"
        );
        $("#id_account_primary_finance_admin").val(
          response.data.primary_finance_admin.name +
            " (" +
            response.data.primary_finance_admin.email +
            ")"
        );
        $("#id_account_hotel_finance_poc").val(
          response.data.hotel_finance_poc.name +
            " (" +
            response.data.hotel_finance_poc.email +
            ")"
        );
      });
    };
  });
})(grp.jQuery);