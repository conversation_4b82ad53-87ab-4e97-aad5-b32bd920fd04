from dal import autocomplete
from django.contrib import admin

from b2b.admin import CustomPermissionsMixin, forms
from b2b.admin.utils.make_id import make_id
from b2b.constants import IntegrationEventStatus, B2BEventType, RecordChangeReason
from b2b.csv_actions.utils.common_utils import generate_event_id
from b2b.dto.integration_event import IntegrationEventDTO
from b2b.models import Account, AccountVersion, AccountLegalEntity, Corporate, LegalEntity, User
from b2b.models.integration_event import IntegrationEvent
from b2b.utils.legal_entity_utils import create_legal_entity_dto


class AccountModelForm(forms.ModelForm):
    corporate = forms.ModelChoiceField(
        queryset=Corporate.objects.all(),
        widget=autocomplete.ModelSelect2(
            url='corporates-autocomplete',
        ),
    )
    treebo_poc = forms.ModelChoiceField(
        queryset=User.objects.all(),
        widget=autocomplete.ModelSelect2(
            url='users-autocomplete',
        ),
    )
    primary_admin = forms.ModelChoiceField(
        queryset=User.objects.all(),
        widget=autocomplete.ModelSelect2(
            url='users-autocomplete',
        ),
    )
    inside_sales_poc = forms.ModelChoiceField(
        queryset=User.objects.all(),
        widget=autocomplete.ModelSelect2(
            url='users-autocomplete',
        ),
    )
    primary_finance_admin = forms.ModelChoiceField(
        queryset=User.objects.all(),
        widget=autocomplete.ModelSelect2(
            url='users-autocomplete',
        ),
    )
    hotel_finance_poc = forms.ModelChoiceField(
        queryset=User.objects.all(),
        widget=autocomplete.ModelSelect2(
            url='users-autocomplete',
        ),
        required=False,
    )

    class Meta:
        model = Account
        fields = ["corporate", "treebo_poc", "primary_admin", "inside_sales_poc",
                  "primary_finance_admin", "hotel_finance_poc", "is_active"]

    def __init__(self, *args, **kwargs):
        super(AccountModelForm, self).__init__(*args, **kwargs)
        obj = kwargs.get('instance')
        if obj:
            self.fields["corporate"].initial = obj.corporate
            self.fields["corporate"].widget.attrs['disabled'] = 'disabled'

            # Since object in self.data is immutable, we will update and replace it with a duplicate mutable object.
            mutable_data = self.data.copy()
            mutable_data["corporate"] = obj.corporate_id
            self.data = mutable_data

    def clean(self):
        super().clean()
        default_counts = 0
        for form_id in range(int(self.data["accountlegalentity_set-TOTAL_FORMS"])):
            field_name = f"accountlegalentity_set-{form_id}-is_default"
            is_deleted = f"accountlegalentity_set-{form_id}-DELETE"
            if self.data.get(field_name, "off") == "on" and self.data.get(is_deleted, "off") != "on":
                default_counts += 1
        if default_counts > 1:
            raise forms.ValidationError("There should be exactly one default legal entity.")


class AccountLegalEntityForm(forms.ModelForm):
    legal_entity = forms.ModelChoiceField(
        queryset=LegalEntity.objects.all(),
        widget=autocomplete.ModelSelect2(
            url='legal-entity-autocomplete',
            forward=('corporate',),
            attrs={'style': 'width:500px;'},
        ),
    )

    class Meta:
        model = AccountLegalEntity
        fields = ["legal_entity", "is_default"]

    def __init__(self, *args, **kwargs):
        super(AccountLegalEntityForm, self).__init__(*args, **kwargs)
        obj = kwargs.get('instance')
        if obj:
            # Since object in self.data is immutable, we will update and replace it with a duplicate mutable object.
            mutable_data = self.data.copy()
            mutable_data["corporate"] = str(obj.legal_entity.corporate.id)
            self.data = mutable_data

    def clean_legal_entity(self):
        legal_entity = self.cleaned_data["legal_entity"]
        if self.data.get('corporate') and str(legal_entity.corporate.id) != self.data['corporate']:
            raise forms.ValidationError("Legal Entity should belong to the selected Corporate.")
        return legal_entity

    def clean(self):
        cleaned_data = super(AccountLegalEntityForm, self).clean()
        if cleaned_data.get("is_default") and cleaned_data.get("legal_entity") and not cleaned_data["legal_entity"].active:
            self.add_error("legal_entity", "LegalEntity status is set as Inactive. Please Make it Active First.")

        return cleaned_data


class AccountLegalEntityInline(admin.TabularInline):
    model = AccountLegalEntity
    form = AccountLegalEntityForm
    extra = 0
    fields = ('legal_entity', 'is_default')


class AccountModelAdmin(CustomPermissionsMixin):
    form = AccountModelForm
    readonly_fields = ("account_id", "is_active")
    list_display = ("get_corporate_name", "get_corporate_id", "get_account_id", "get_account_default_le",
                    "is_active", "treebo_poc", "primary_admin", "created_at", "modified_at",)
    list_display_links = ("get_corporate_name", "get_corporate_id", "get_account_id",)
    search_fields = ("account_id", "corporate__corporate_id", "corporate__trading_name",
                     "treebo_poc__email", "primary_admin__email", "accountlegalentity__legal_entity__legal_entity_id",
                     "accountlegalentity__legal_entity__name")
    list_filter = [
        ("accountlegalentity__legal_entity", admin.RelatedOnlyFieldListFilter),
    ]

    inlines = (AccountLegalEntityInline,)
    ordering = ('-modified_at',)

    def get_corporate_name(self, obj):
        return obj.corporate.trading_name

    def get_corporate_id(self, obj):
        return obj.corporate.corporate_id

    def get_account_id(self, obj):
        return obj.account_id

    def get_account_default_le(self, obj):
        return getattr(obj.default_legal_entity(), 'legal_entity_id', '-')

    get_corporate_name.short_description = "Corporate Name"
    get_corporate_id.short_description = "Corporate ID"
    get_account_id.short_description = "Account ID"
    get_account_default_le.short_description = "Default Legal Entity"

    def has_delete_permission(self, request, obj=None):
        return False

    def get_actions(self, request):
        actions = super(CustomPermissionsMixin, self).get_actions(request)
        actions.pop("delete_selected", None)
        return actions

    def save_model(self, request, obj, form, change):
        if not obj.account_id:
            obj.account_id = f"acc-{make_id(obj.corporate.trading_name)}"
        obj.updated_by = request.user

        default_counts = 0
        for form_id in range(int(form.data["accountlegalentity_set-TOTAL_FORMS"])):
            field_name = f"accountlegalentity_set-{form_id}-is_default"
            is_deleted = f"accountlegalentity_set-{form_id}-DELETE"
            if form.data.get(field_name, "off") == "on" and form.data.get(is_deleted, "off") != "on":
                default_counts += 1
        obj.is_active = default_counts == 1

        obj._change_reason = RecordChangeReason.UPDATED_VIA_ADMIN

        super(AccountModelAdmin, self).save_model(request, obj, form, change)

    def save_related(self, request, form, formsets, change):
        previous_default_entity = current_default_entity = None
        update_legal_entities = list()
        for formset in formsets:
            if formset.model == AccountLegalEntityInline.model:
                for accountlegalentityform in formset.forms:
                    cleaned_data = accountlegalentityform.cleaned_data
                    if cleaned_data.get('is_default') and not cleaned_data.get('DELETE'):
                        current_default_entity = cleaned_data['legal_entity']

        previous_default_account_legal_entity = (AccountLegalEntity.objects.select_related('legal_entity')
                                                 .filter(account_id=form.instance.account_id, is_default=True)
                                                 .first())
        if previous_default_account_legal_entity:
            previous_default_entity = previous_default_account_legal_entity.legal_entity

        if previous_default_entity != current_default_entity:
            update_legal_entities.append(current_default_entity)
            update_legal_entities.append(previous_default_entity)
            if previous_default_account_legal_entity:
                previous_default_account_legal_entity.is_default = False
                previous_default_account_legal_entity.save()
        elif change:
            update_legal_entities.append(current_default_entity)

        super().save_related(request, form, formsets, change)

        for legal_entity in update_legal_entities:
            if not legal_entity:
                continue
            account = None
            if legal_entity == current_default_entity:
                account = form.instance
            event_dto = IntegrationEventDTO(
                data=dict(
                    event_id=generate_event_id(),
                    event_type=B2BEventType.LEGAL_ENTITY_UPDATED,
                    body=create_legal_entity_dto(legal_entity=legal_entity, account=account),
                    status=IntegrationEventStatus.UNPUBLISHED,
                    generated_by_id=request.user.id,
                )
            )
            if event_dto.is_valid():
                IntegrationEvent.objects.create(**event_dto.data)


class AccountVersionAdmin(CustomPermissionsMixin):
    list_display = ('get_corporate', 'get_account_id', 'version', 'treebo_poc', 'primary_admin', 'created_by', 'created_at')
    search_fields = ('account__account_id', 'corporate__corporate_id', 'corporate__trading_name',
                     "treebo_poc__email", "primary_admin__email")
    readonly_fields = ('corporate', 'account', 'version', 'treebo_poc', 'primary_admin', 'created_by', 'created_at')
    ordering = ('-modified_at',)

    def get_corporate(self, obj):
        return f"{obj.corporate.trading_name} {obj.corporate.corporate_id}"

    def get_account_id(self, obj):
        return obj.account.account_id

    get_corporate.short_description = "Corporate"
    get_account_id.short_description = "Account ID"

    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def change_view(self, request, object_id, form_url='', extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_save_and_add_another'] = False
        extra_context['show_save_and_continue'] = False
        extra_context['show_save'] = False
        return super(AccountVersionAdmin, self).change_view(request, object_id, extra_context=extra_context)

    def get_actions(self, request):
        actions = super(AccountVersionAdmin, self).get_actions(request)
        actions.pop("delete_selected", None)
        return actions


admin.site.register(Account, AccountModelAdmin)
admin.site.register(AccountVersion, AccountVersionAdmin)
