import hashlib
import string
import time
from codecs import encode


def make_id(text):
    # the trailing space is intentional after 'aeiou'
    consonants = [alphabet for alphabet in string.ascii_lowercase if alphabet not in 'aeiou']
    name_consonants = ''.join([t for t in text.lower() if t in consonants])
    hex_val = hashlib.sha224(encode(name_consonants + str(time.time()))).hexdigest()
    return name_consonants[:4].lower() + hex_val[:4]
