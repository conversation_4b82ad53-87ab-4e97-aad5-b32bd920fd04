from datetime import date, timedelta, datetime


def get_intermediate_dates(date1, date2):
    """
    Get dates between two dates
    Args:
        date1: Start Date string in 'dd-mm-yyyy' format
        date2: End Date string in 'dd-mm-yyyy' format

    Returns: List of string representation of dates between date1 and date2

    """
    date1 = datetime.strptime(date1, '%Y-%m-%d')
    date2 = datetime.strptime(date2, '%Y-%m-%d')

    delta = date2 - date1

    return [date1 + timedelta(days=i) for i in range(delta.days)]
