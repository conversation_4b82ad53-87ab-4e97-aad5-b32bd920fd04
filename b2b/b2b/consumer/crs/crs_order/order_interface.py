# -*- coding: utf-8 -*-
import abc


class CRSBookingOrderInterface(object, metaclass=abc.ABCMeta):
    """
    interface for adapting various booking objects exposed by
    different systems (like our own b2b, hx, new crs etc) to
    """

    @abc.abstractmethod
    def check_in(self):
        pass

    @abc.abstractmethod
    def check_out(self):
        pass

    @abc.abstractmethod
    def order_id(self):
        pass

    @abc.abstractmethod
    def soft_booking_expiry(self):
        pass

    @abc.abstractmethod
    def total_payment(self):
        pass

    @abc.abstractmethod
    def payment_type(self):
        pass

    @abc.abstractmethod
    def get_payterm(self):
        pass

    @abc.abstractmethod
    def get_booking_source(self):
        pass

    @abc.abstractmethod
    def bookedroom_set(self):
        pass

    @abc.abstractmethod
    def gstin_details(self):
        pass

    def __repr__(self):
        return "<{cls}: {i}>".format(cls=self.__class__.__name__, i=self.order_id())
