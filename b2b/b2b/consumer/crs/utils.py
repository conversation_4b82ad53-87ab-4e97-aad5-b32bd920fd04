import datetime
import pytz
from django.conf import settings


def convert_to_utc_timezone(datetime_val):
    """
    converts given datetime string to utc timezone
    Returns:

    """
    local = pytz.timezone(settings.TIME_ZONE)
    naive = datetime.datetime.strptime(datetime_val, "%Y-%m-%d %H:%M:%S")
    local_dt = local.localize(naive, is_dst=None)
    utc_dt = local_dt.astimezone(pytz.utc)
    return utc_dt.strftime("%Y-%m-%d %H:%M:%S")


def convert_to_local_timezone(datetime_val):
    """
    converts given datetime string to utc timezone
    Returns:

    """
    local = pytz.timezone(settings.TIME_ZONE)
    _val = datetime_val
    if not isinstance(_val, str):
        _val = datetime.datetime.strftime(_val, "%Y-%m-%d %H:%M:%S")
    naive = datetime.datetime.strptime(_val, "%Y-%m-%d %H:%M:%S")
    utc = naive.replace(tzinfo=pytz.utc)
    local_dt = utc.astimezone(local)
    return local_dt.strftime("%Y-%m-%d %H:%M:%S")


def full_name(first_name, last_name):
    """
    Joins first name and last name to return full name
    Args:
        first_name:
        last_name:

    Returns:

    """
    return ' '.join([_f for _f in (first_name, last_name) if _f])


def split_name(name):
    """
    Splits name based on space and return first name and last name
    Args:
        name:

    Returns:

    """
    first_name_last_name = name.split(" ")
    first_name = first_name_last_name[0] if len(first_name_last_name) > 0 else ''
    last_name = first_name_last_name[1] if len(first_name_last_name) > 1 else ''
    return first_name, last_name


def convert_month_name_datetime_string(datetimestring, hours):

    local = pytz.timezone(settings.TIME_ZONE)
    _val = datetimestring
    if not isinstance(datetimestring, str):
        _val = datetime.datetime.strftime(_val, "%Y-%m-%d %H:%M:%S")
    naive = datetime.datetime.strptime(_val, "%Y-%m-%d %H:%M:%S")
    naive = naive - datetime.timedelta(hours=hours)
    utc = naive.replace(tzinfo=pytz.utc)
    local_dt = utc.astimezone(local)
    day = local_dt.day
    if day % 10 == 1:
        return str(local_dt.strftime('%I:%M %p, %dst %B, %Y'))
    elif day % 10 == 2:
        return str(local_dt.strftime('%I:%M %p, %dnd %B, %Y'))
    elif day % 10 == 3:
        return str(local_dt.strftime('%I:%M %p, %drd %B, %Y '))
    return str(local_dt.strftime('%I:%M %p, %dth %B, %Y'))
