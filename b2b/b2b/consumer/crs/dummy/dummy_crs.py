# -*- coding: utf-8 -*-
# pylint: disable=abstract-method
import logging

from b2b.consumer.crs.crs_backend import CRSBackend


class DummyCRS(CRSBackend):
    @classmethod
    def confirm_booking(cls, booking_id):
        logger = logging.getLogger(cls.__class__.__name__)
        logger.info('confirm_booking(booking_id=%s)', booking_id)

    @classmethod
    def cancel_booking(cls, booking_id):
        logger = logging.getLogger(cls.__class__.__name__)
        logger.info('cancel_booking(booking_id=%s)', booking_id)

    @classmethod
    def update_booking(cls, booking_id):
        logger = logging.getLogger(cls.__class__.__name__)
        logger.info('update_booking(booking_id=%s)', booking_id)

    @classmethod
    def register_corporate(cls, corporate_id, **kwargs):
        logger = logging.getLogger(cls.__class__.__name__)
        logger.info('register_corporate(corporate_id=%s)', corporate_id)

    @classmethod
    def save_booking(cls, booking_id):
        logger = logging.getLogger(cls.__class__.__name__)
        logger.info('save_booking(booking_id=%s)', booking_id)
