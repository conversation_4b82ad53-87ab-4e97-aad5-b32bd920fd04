import json


class TreeboCRSException(Exception):

    def __init__(self, errors, message=None):

        self.errors = errors

        if not message:
            message = ','.join([str(e.get('developer_message') or '') for e in errors])

        if not message:
            message = ','.join([json.dumps(e) for e in errors])

        super(TreeboCRSException, self).__init__('TreeboCRSError: {m}'.format(m=message))
