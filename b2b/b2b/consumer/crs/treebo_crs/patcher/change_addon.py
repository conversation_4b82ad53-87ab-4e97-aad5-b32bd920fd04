import logging

from b2b.consumer.crs.treebo_crs.utils_mixin import thsc_exception_transformer
from core.booking.diff import ChangeAddonDiff
from common.diff_and_patch.patch_item import PatchItem

logger = logging.getLogger(__name__)


class ChangeAddonPatch(PatchItem):

    @classmethod
    def diff_class(cls):
        return ChangeAddonDiff

    @thsc_exception_transformer
    def apply(self):
        raise RuntimeError('Do not know how to change an addon.')
