import logging

from b2b.consumer.crs.treebo_crs.utils_mixin import thsc_exception_transformer
from core.booking.diff import SoftBlockDiff
from common.diff_and_patch.patch_item import PatchItem

logger = logging.getLogger(__name__)


class SoftBlockPatch(PatchItem):

    @classmethod
    def diff_class(cls):
        return SoftBlockDiff

    @thsc_exception_transformer
    def apply(self):
        thsc_booking = self.patcher.thsc_booking_for_update()

        if self.delta.new_state is None:
            thsc_booking.confirm()
        else:
            thsc_booking.hold_till = self.delta.new_state
            thsc_booking.update()
