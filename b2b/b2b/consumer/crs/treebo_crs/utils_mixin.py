# pylint: disable=invalid-name, no-member
import functools
import logging
import sys

from django.contrib.auth.models import AnonymousUser
from django_currentuser.middleware import get_current_user
from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes, PaymentModes, PaymentStatus, \
    PaymentChannels, PaymentTypes, PaymentReceiverTypes, PaymentInstruction
from ths_common.constants.booking_constants import AgeGroup
from ths_common.exceptions import CRSException
from ths_common.value_objects import NotAssigned
from thsc.crs import context as thsc_context
from thsc.crs.entities.billing import Payment as THSCPayment
from thsc.crs.entities.booking import GuestStay
from thsc.crs.entities.booking import Price
from thsc.crs.entities.customer import Customer
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from b2b.api.constants import VipDetails
from b2b.consumer.crs.treebo_crs.exceptions import TreeboCRSException
from common.utils.misc import get_request_id
from core.booking.data_classes.guest import Adult, Child, Infant
from core.booking.data_classes.payment_type import (CorporateBTC, CorporateBTT, CorporateDirect,
                                                    TaPaidByTa, TaPaidByGuest)
from core.booking.data_classes.payments import (PrepaidPaymentMode, TCRPaymentMode, ConfirmedPaymentStatus,
                                                CancelledPaymentStatus, BTTPaymentMode, PrimusPaymentPOS,
                                                PaymentLinkPaymentPOS, RazorpayPaymentGateway)

logger = logging.getLogger(__name__)


def get_thsc_default_payment_instruction(payment_type):
    if payment_type == CorporateBTC:
        return PaymentInstruction.PAY_AFTER_CHECKOUT.value
    return PaymentInstruction.PAY_AT_CHECKOUT.value


class ThscUtils(object):

    @classmethod
    def thsc_prices_from_prices(cls, prices, payment_type, rate_plan_reference_id=None):
        bill_to, credit_type = cls.get_bill_to_and_credit_type_from_payment_type(payment_type)

        thsc_prices = [Price(pretax_amount=Money(price.pre_tax, CurrencyType.INR),
                             applicable_date=price.date,
                             bill_to_type=bill_to,
                             type=credit_type,
                             rate_plan_reference_id=rate_plan_reference_id or NotAssigned
                             )
                       for price in prices]
        return thsc_prices

    @classmethod
    def thsc_guest_stay_from_guest(cls, guest):
        thsc_customer = cls._thsc_customer_from_guest(guest)
        age_group = cls._age_group_from_guest(guest)
        return GuestStay(age_group=age_group, guest=thsc_customer)

    @classmethod
    def thsc_guest_stay_from_guest_stay(cls, guest_stay):
        thsc_guest_stay = cls.thsc_guest_stay_from_guest(guest_stay.guest)
        thsc_guest_stay.checkin_date = guest_stay.checkin
        thsc_guest_stay.checkout_date = guest_stay.checkout
        return thsc_guest_stay

    @classmethod
    def _thsc_customer_from_guest(cls, guest):
        thsc_customer = Customer(first_name=guest.first_name)

        if guest.last_name:
            thsc_customer.last_name = guest.last_name

        if guest.email:
            thsc_customer.email = guest.email

        if guest.phone_number:
            thsc_customer.phone_number = guest.phone_number
        if hasattr(guest, 'is_vip'):
            thsc_customer.is_vip = guest.is_vip
            thsc_customer.vip_details = {"status": VipDetails.VIP_LEVEL_1, "details": None}
        return thsc_customer

    @classmethod
    def thsc_bill_to_from_payment_type(cls, payment_type):
        mapping = {
            CorporateDirect: ChargeBillToTypes.GUEST,
            CorporateBTC: ChargeBillToTypes.COMPANY,
            CorporateBTT: ChargeBillToTypes.COMPANY,
            TaPaidByTa: ChargeBillToTypes.GUEST,
            TaPaidByGuest: ChargeBillToTypes.GUEST,
        }

        return mapping[payment_type]

    @classmethod
    def thsc_credit_type_from_payment_type(cls, payment_type):
        mapping = {
            CorporateDirect: ChargeTypes.NON_CREDIT,
            CorporateBTC: ChargeTypes.CREDIT,
            CorporateBTT: ChargeTypes.NON_CREDIT,
            TaPaidByTa: ChargeTypes.NON_CREDIT,
            TaPaidByGuest: ChargeTypes.NON_CREDIT,
        }

        return mapping[payment_type]

    @classmethod
    def get_bill_to_and_credit_type_from_payment_type(cls, payment_type):
        bill_to = cls.thsc_bill_to_from_payment_type(payment_type)
        credit_type = cls.thsc_credit_type_from_payment_type(payment_type)

        return bill_to, credit_type

    @classmethod
    def set_thsc_context(cls, application):
        thsc_context.application = application.uid
        current_user = get_current_user()
        if current_user and not isinstance(current_user, AnonymousUser):
            thsc_context.email = current_user.email
            thsc_context.user = current_user.name
            thsc_context.request_id = get_request_id()
        return thsc_context

    @classmethod
    def _age_group_from_guest(cls, guest):
        if guest.age in Adult:
            return AgeGroup.ADULT
        elif guest.age in Child:
            return AgeGroup.CHILD
        elif guest.age in Infant:
            return AgeGroup.INFANT
        logger.warning('Invalid age: {}'.format(guest.age))
        return AgeGroup.ADULT

    @classmethod
    def get_thsc_payment_mode_from_payment(cls, payment):
        if payment.mode == PrepaidPaymentMode:
            if not payment.gateway == RazorpayPaymentGateway:
                raise ValueError('{g} is not supported yet'.format(g=payment.gateway))
            if payment.pos in [PrimusPaymentPOS, PaymentLinkPaymentPOS]:
                return PaymentModes.RAZORPAY_API
            else:
                raise ValueError('Invalid payment pos:  {p}'.format(p=payment.pos))
        elif payment.mode == TCRPaymentMode:
            return PaymentModes.TREEBO_CORPORATE_REWARDS
        elif payment.mode == BTTPaymentMode:
            return PaymentModes.PAID_BY_TREEBO
        else:
            raise ValueError('Invalid payment mode:  {p}'.format(p=payment.mode))

    @classmethod
    def get_payment_mode_from_thsc_payment_mode(cls, thsc_payment_mode):
        if thsc_payment_mode == PaymentModes.TREEBO_CORPORATE_REWARDS:
            return TCRPaymentMode
        elif thsc_payment_mode == PaymentModes.PAID_BY_TREEBO:
            return BTTPaymentMode
        return PrepaidPaymentMode

    @classmethod
    def get_payment_pos_from_thsc_payment_mode(cls, thsc_payment_mode):
        if thsc_payment_mode == PaymentModes.RAZORPAY_PAYMENT_GATEWAY:
            return PrimusPaymentPOS
        elif thsc_payment_mode == PaymentModes.RAZORPAY_API:
            return PaymentLinkPaymentPOS
        return PrimusPaymentPOS

    @classmethod
    def get_payment_gateway_from_thsc_payment_mode(cls, thsc_payment_mode):
        if thsc_payment_mode in (PaymentModes.RAZORPAY_PAYMENT_GATEWAY, PaymentModes.RAZORPAY_API):
            return RazorpayPaymentGateway
        return None  # for btt or tcr

    @classmethod
    def get_thsc_payment_status_from_payment_status(cls, payment_status):
        if payment_status == ConfirmedPaymentStatus:
            return PaymentStatus.DONE
        elif payment_status == CancelledPaymentStatus:
            return PaymentStatus.CANCELLED
        return PaymentStatus.PENDING

    @classmethod
    def get_payment_status_from_thsc_payment_status(cls, thsc_payment_status):
        if thsc_payment_status == PaymentStatus.DONE or thsc_payment_status == PaymentStatus.POSTED:
            return ConfirmedPaymentStatus
        elif thsc_payment_status == PaymentStatus.CANCELLED:
            return CancelledPaymentStatus
        else:
            raise ValueError('Invalid payment status:  {p}'.format(p=thsc_payment_status))

    @classmethod
    def get_thsc_payments_from_payments(cls, payments, refund=False):
        thsc_payments = []
        for payment in payments:
            payment_mode = ThscUtils.get_thsc_payment_mode_from_payment(payment)
            if payment_mode == PaymentModes.TREEBO_CORPORATE_REWARDS:
                paid_by = PaymentReceiverTypes.CORPORATE
            elif payment_mode == PaymentModes.PAID_BY_TREEBO:
                paid_by = PaymentReceiverTypes.TREEBO
            else:
                paid_by = PaymentReceiverTypes.GUEST
            thsc_payment = THSCPayment(amount=Money(payment.amount, CurrencyType.INR),
                                       date_of_payment=payment.date,
                                       payment_ref_id=payment.reference_id,
                                       payment_channel=PaymentChannels.ONLINE,
                                       payment_type=PaymentTypes.PAYMENT if not refund else PaymentTypes.REFUND,
                                       payment_mode=payment_mode,
                                       paid_to=PaymentReceiverTypes.TREEBO,
                                       paid_by=paid_by,
                                       status=ThscUtils.get_thsc_payment_status_from_payment_status(payment.status),
                                       )
            thsc_payments.append(thsc_payment)
        return thsc_payments


def thsc_exception_transformer(function):
    """ This is required since CRSException does not set message every time which is expected by many packages"""

    @functools.wraps(function)
    def wrapper(*args, **kwargs):
        try:
            function(*args, **kwargs)
        except CRSException as e:
            # raises TreeboCRSException with same traceback as CRSException.
            # pylint: disable=old-raise-syntax
            raise TreeboCRSException(e.extra_payload, e.message).with_traceback(sys.exc_info()[2])

    return wrapper


def get_extra_information_from_booking(booking):
    return {'payment_type': booking.payment_type.name,
            'pricing_scheme': booking.pricing_scheme.name()}
