# pylint: skip-file
import logging

from b2b.constants import HotelAttributes
from b2b.consumer.hotel_details import BaseHotelDetailsBackend
from b2b.consumer.hotel_details.cataloging_service.catalog_service import CatalogService
from b2b.domain.services.exceptions import InvalidDTO
from b2b.dto.hotel_details_request import HotelDetailsDTO
from b2b.models import Hotel
from b2b.models.state import State

logger = logging.getLogger(__name__)


class CatalogingServiceBackend(BaseHotelDetailsBackend):

    @classmethod
    def get_hotel_details(cls, hotel_details_dto):
        assert isinstance(hotel_details_dto, HotelDetailsDTO)
        if not hotel_details_dto.is_valid():
            logger.info("Invalid Hotel Details DTO passed to CatalogingServiceBackend , errors: {err}".format(
                err=hotel_details_dto.errors))
            raise InvalidDTO(hotel_details_dto)
        hotels = hotel_details_dto.data['hotels']
        logger.info('Getting Hotel details for hotels: {h}'.format(h=hotels))
        hotel_ids = hotels.split(',')
        hotel_cs_ids = Hotel.objects.filter(hotel_id__in=hotel_ids, catalogue_id__isnull=False).values_list('catalogue_id', flat=True)
        if not hotel_cs_ids:
            logger.info("Hotels does not exist:- for "
                            "hotel_ids {hotel_ids}".format(hotel_ids=hotel_ids))
            raise Exception
        data = []
        catalog_service = CatalogService()
        for cs_id in hotel_cs_ids:
            try:
                property_detail = catalog_service.get_property(catalog_id=cs_id)
            except Exception as e:
                logger.info(e)
                raise e
            data.append(property_detail)
        return {'data': data}

    @classmethod
    def get_all_hotels_status(cls):
        """
        Instead of making an api call we will just return the hotel_ids, with active = True
        :return:
        """
        hotels = Hotel.objects.filter(active=True)
        hotel_by_status = {h.hotel_id: h.active for h in hotels}
        logger.info("All Hotel Status data : {data}".format(data=hotel_by_status))
        return hotel_by_status

    @classmethod
    def get_all_hotels(cls):
        logger.info("Sending all active hotels")
        data = []
        hotels = Hotel.objects.filter(active=True).exclude(hotel_id='')
        for hotel in hotels:
            state = cls.get_state_by_hotel(hotel)
            _ = dict(id=hotel.hotel_id, name=hotel.name, description=hotel.description, city=hotel.city, state=state,
                     hotelogix_id=hotel.hotelogix_id, phone_number=hotel.phone_number,
                     room_count=hotel.get_attr(HotelAttributes.ROOM_COUNT),
                     checkin_time=hotel.get_attr(HotelAttributes.CHECKIN_TIME),
                     checkout_time=hotel.get_attr(HotelAttributes.CHECKOUT_TIME), locality=hotel.locality,
                     street=hotel.street, latitude=hotel.get_attr(HotelAttributes.LATITUDE),
                     longitude=hotel.get_attr(HotelAttributes.LONGITUDE), status=hotel.active)
            data.append(_)
        logger.info("Total hotels : {count}".format(count=len(hotels)))
        return {"status": "success", "data": data}

    @classmethod
    def get_state_by_hotel(cls, hotel):
        try:
            state = State.objects.get(ext_state_id=hotel.state_id)
            res = state.name
        except State.DoesNotExist as e:
            res = ""
        return res
