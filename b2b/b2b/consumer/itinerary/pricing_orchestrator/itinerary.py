import logging

from django.conf import settings

from b2b.consumer.hotel_pricing.pricing_orchestrator.po_pricing_details import WebsitePriceDetails
from b2b.consumer.itinerary.interface import BaseItineraryBackend
from b2b.domain.services.exceptions import InvalidDTO
from b2b.models import LegalEntity

logger = logging.getLogger(__name__)


class ItineraryBackend(BaseItineraryBackend):
    @classmethod
    def get_itinerary_data(cls, dto):

        if not dto.is_valid():
            raise InvalidDTO(dto)
        itinerary_request = dto.data
        try:
            hotel_id = itinerary_request['hotel_id']
            _legal_entity = LegalEntity.objects.get(legal_entity_id=itinerary_request['legal_entity_id'])
            ta_pricing_applicable = _legal_entity.is_web_pricing_for_ta_or_tmc_applicable()

            web_price_details_cls = WebsitePriceDetails(check_in=itinerary_request['check_in'],
                                                        checkout=itinerary_request['check_out'],
                                                        hotel_ids=[hotel_id],
                                                        room_config=itinerary_request['room_config'],
                                                        pricing_type=settings.PRICING_TYPES['ITINERARY'],
                                                        ta_pricing_applicable=ta_pricing_applicable,
                                                        gstin=itinerary_request['gstin']
                                                        )
            itinerary_response = web_price_details_cls.get_website_price_for_particular_room_type(hotel_id,
                                                                                                  itinerary_request[
                                                                                                      'room_type'])
            itinerary_response = itinerary_response[0]

            return itinerary_response

        except Exception as e:

            logger.info("ItineraryBackend:- error occurred while getting itinerary details for "
                         "hotel id {hotel_id}: {err}".format(hotel_id=itinerary_request['hotel_id'],
                                                             err=str(e)))
            raise
