import json
import logging
import uuid

from django.conf import settings
from qwikcilver.client import QwikcilverClient

from b2b import constants
from b2b.api.request_dtos import CorporatesRequestDTO
from b2b.domain.services.corporate import CorporateService
from b2b.domain.services.exceptions import InvalidDTO
from b2b.models import AccountLegalEntity, AccountAdmin
from common.utils.slack import send_slack_notif
from loyalty.consumer.catelog_config.qwikcilver_config.qwikcilver_config import QwikcilverConfig
from loyalty.consumer.gift_card_order.gift_card_order_interface import BaseGiftCardOrder
from loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.b2b_qwikcilver_authstore import B2BQwikcilverAutoStore
from loyalty.dto.gift_card_order_dto import ProductListDto, GiftCardArrayDTO
from loyalty.dto.order_config_dto import OrderConfigDTO
from loyalty.dto.qwikcilver_gift_card_dto import AddressDTO, ProductDTO, PlaceOrderDTO, PaymentMethodDTO
from loyalty.exceptions import QwikcilverProductDoesntExist, InvalidCatalogItem, InvalidCorporatePassed, \
    InvalidQwikcilverItemPrice, CatalogProductDoesNotExist, ProductUnAvailableOnQwikcilver, QwikcilverSpendCallFailed
from loyalty.exceptions import TotalGiftCardLimitExceeds, GiftCardQuantityExceeds, QwikcilverBookingFailed, \
    QwikcilverOrderStatusNotCompleted
from loyalty.models.loyalty_order import LoyaltyOrder
from loyalty.models.qwikcilver_orders import QwikcilverOrders
from loyalty.models.qwikcilver_products import QwikcilverProducts
from loyalty.models.treebo_catelog_product import TreeboCatelogProduct
from loyalty.models.treebo_catelog_product_item import TreeboCatelogItem

logger = logging.getLogger(__name__)


class QwikcilverGiftCardOrder(BaseGiftCardOrder):
    @classmethod
    def get_name(cls):
        return cls.name

    @classmethod
    def get_gift_cards(cls, loyalty_order_id):
        qs_gift_card_orders = cls._get_successful_qwikcilver_orders(loyalty_order_id=loyalty_order_id)
        logger.info("Gift Card Orders : {qs_gift_card_orders}".format(qs_gift_card_orders=qs_gift_card_orders))
        gift_card_array = cls._convert_qs_product_name_to_treebo_catalog_name(qs_gift_card_orders)

        gift_card_array_dto = GiftCardArrayDTO(data={"gift_cards": gift_card_array})
        if not gift_card_array_dto.is_valid():
            msg = "[QwikcilverGiftCardOrder]Get Gift Cards : {loyalty_order_id} : {errors}".format(
                loyalty_order_id=loyalty_order_id, errors=gift_card_array_dto.errors)
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            raise InvalidDTO(gift_card_array_dto)

        return gift_card_array_dto

    @classmethod
    def _convert_qs_product_name_to_treebo_catalog_name(cls, qs_gift_card_orders):
        gift_card_array = []

        qs_product_names = list(set([_tuple[0] for _tuple in qs_gift_card_orders]))
        rows = QwikcilverProducts.objects.filter(name__in=qs_product_names)
        qs_product_name_vs_ids = {r.name: r.treebo_catelog_product_id for r in
                                  rows}  # { "qs_name" : "treebo_catalog_id"}
        treebo_catelog_product_ids = [product.treebo_catelog_product_id for product in rows]
        catalog_products = TreeboCatelogProduct.objects.filter(id__in=treebo_catelog_product_ids)
        treebo_catelog_ids_vs_product_details = {
        c.id: {"name": c.name, "img_url": c.img_url, "description": c.description,
               "terms_and_condition": c.terms_and_condition, "tnc_link": c.tnc_link, "powered_by": c.powered_by} for c
        in catalog_products}  # { "catalog_product_id" : ["catalog_product_name", "catalog_product_image_url"]}

        for _tuple in qs_gift_card_orders:
            qs_product_name = _tuple[0]
            temp_gift_card = {
                "name": treebo_catelog_ids_vs_product_details[qs_product_name_vs_ids[qs_product_name]]['name'],
                "image_url": treebo_catelog_ids_vs_product_details[qs_product_name_vs_ids[qs_product_name]]["img_url"],
                "cardnumber": _tuple[1],
                "price": _tuple[2],
                "expiry_date": _tuple[3],
                "pin": _tuple[4],
                "description": treebo_catelog_ids_vs_product_details[qs_product_name_vs_ids[qs_product_name]][
                    'description'],
                "terms_and_condition": treebo_catelog_ids_vs_product_details[qs_product_name_vs_ids[qs_product_name]][
                    "terms_and_condition"],
                "tnc_link": treebo_catelog_ids_vs_product_details[qs_product_name_vs_ids[qs_product_name]]['tnc_link'],
                "powered_by": treebo_catelog_ids_vs_product_details[qs_product_name_vs_ids[qs_product_name]][
                    'powered_by'],
            }
            gift_card_array.append(temp_gift_card)

        return gift_card_array

    @classmethod
    def _get_successful_qwikcilver_orders(cls, loyalty_order_id):
        qwikcilver_order_entries = QwikcilverOrders.objects.filter(loyalty_order_id=loyalty_order_id,
                                                                   process_status='processed', success=True)

        all_products = []

        for orders in qwikcilver_order_entries:
            cards = json.JSONDecoder().decode(orders.carddetails)

            # converting to a flat structure
            # [ ( 'qs_product_name', cardnumber , 'price , 'expiry_date', 'pin' ) ]

            all_products += [(card, card_details['cardnumber'], card_details['card_price'], card_details['expiry_date'],
                              card_details['pin_or_url']) for card in cards for card_details in cards[card]]

        return all_products

    @classmethod
    def _get_qwikcilver_client_instance(cls):
        auth_store = B2BQwikcilverAutoStore()
        client = QwikcilverClient(qwikcilver_config=settings.QWIKCILVER_CONF, auth_store=auth_store)
        return client

    @classmethod
    def place_order(cls, gift_card_order_data_dto, legal_entity_id, loyalty_order_id, user):

        assert isinstance(gift_card_order_data_dto, ProductListDto)
        assert gift_card_order_data_dto.is_valid()

        products = cls._get_qs_products(gift_card_order_data_dto.data)
        legal_entity_details = cls._get_legal_entity_details(legal_entity_id, user)
        payment_method = cls._get_payment_method(products)
        reference_no = cls._get_reference_number()

        request_object = PlaceOrderDTO(data=dict(
            billing=legal_entity_details,
            shipping=legal_entity_details,
            payment_method=[payment_method],
            refno=reference_no,
            products=products
        ))

        if not request_object.is_valid():
            msg = "[QwikcilverGiftCardOrder] place_order : {loyalty_order_id} : {errors}" \
                .format(loyalty_order_id=loyalty_order_id, errors=request_object.errors)
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            raise InvalidDTO(request_object)

        qs_order_id = cls._add_qs_order_to_database(request_object.data, loyalty_order_id)
        cls._verify_config_validity(request_object)

        qw_client = QwikcilverGiftCardOrder._get_qwikcilver_client_instance()
        response = qw_client.spend(request_object.data)
        logger.info("qwikcilver spend request payload: {payload}".format(payload=request_object.data))
        logger.info("qwikcilver spend response: {response}".format(response=response.json()))
        if response is not None:
            cls._update_qs_order(qs_order_id, response)
            if response.status_code != 201:
                error = response.json()
                qs_message = error.get("message", "")
                logger.info(qs_message)
                if error['error_code'] == 5051:
                    msg = "[QwikcilverGiftCardOrder] place_order : {loyalty_order_id}, error_code : 5051 , {qs_message}" \
                        .format(loyalty_order_id=loyalty_order_id, qs_message=qs_message)
                    send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
                    raise ProductUnAvailableOnQwikcilver(product_json=json.dumps(products))

                msg = "[QwikcilverGiftCardOrder] place_order : Qwikcilver Booking Failed : {loyalty_order_id} , {qs_message}" \
                    .format(loyalty_order_id=loyalty_order_id, qs_message=qs_message)
                send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)

                raise QwikcilverBookingFailed(qs_message)

            if response.json().get("status", "") != 'Complete':
                qs_message = response.json().get("message", "Qwikcilver order not activated")
                logger.info(qs_message)
                msg = "[QwikcilverGiftCardOrder] place_order : Qwikcilver order not activated : {loyalty_order_id}" \
                    .format(loyalty_order_id=loyalty_order_id)
                send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
                raise QwikcilverOrderStatusNotCompleted(loyalty_order_id, qs_order_id)
        else:
            logger.info("Qwikcilver Spend Call Failed")
            msg = "[QwikcilverGiftCardOrder] place_order : {loyalty_order_id} , Qwikcilver Spend Call Failed" \
                .format(loyalty_order_id=loyalty_order_id)
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            raise QwikcilverSpendCallFailed()

    @classmethod
    def _verify_config_validity(cls, place_order_dto):
        # get configs
        config = QwikcilverConfig().get_config()
        assert isinstance(config, OrderConfigDTO)
        assert isinstance(place_order_dto, PlaceOrderDTO)
        if not config.is_valid():
            raise InvalidDTO(config)

        if not place_order_dto.is_valid():
            raise InvalidDTO(place_order_dto)

        config = config.data
        products = place_order_dto.data["products"]
        total_items_in_cart = len(products)

        if total_items_in_cart > int(config['max_item_in_cart']):
            raise TotalGiftCardLimitExceeds(config['max_item_in_cart'])

        for item in products:
            if item['qty'] > int(config['product_max_qty_allow']):
                raise GiftCardQuantityExceeds(config['product_max_qty_allow'])

    @classmethod
    def _get_payment_method(cls, products):
        purchase_order = "Treebo-PO-" + str(uuid.uuid4())
        order_handling_amount = cls._order_handling_charge(products)
        total_amount = cls._get_total_amount(products) + order_handling_amount

        payment_method = PaymentMethodDTO(data=dict(
            method="svc",
            amount_to_redem=total_amount,
            po_number=purchase_order
        ))

        if payment_method.is_valid():
            logger.info("Payment Method : {data}".format(data=payment_method.data))
            return payment_method.data
        else:
            logger.info("Error in payment method DTO")
            raise InvalidDTO(payment_method)

    @classmethod
    def _order_handling_charge(cls, products):
        logger = logging.getLogger(cls.__name__)
        product_id_list = [product['product_id'] for product in products]
        qw_client = QwikcilverGiftCardOrder._get_qwikcilver_client_instance()
        resp = qw_client.get_orderhandling_charge(product_id_list)
        if resp.status_code == 200:
            return int(resp.json()['handling_amount'])
        else:
            logger.warning("Qwikcilver Order Handling Api Call returned non 200 status")
            return 0

    @classmethod
    def _get_total_amount(cls, products):
        total = 0
        for product in products:
            total += int(product['price']) * int(product['qty'])

        return total

    @classmethod
    def _get_legal_entity_details(cls, legal_entity_id, user):
        legal_entity = CorporateService.get_legal_entity_by_legal_entity_id(legal_entity_id)

        auth_user = AccountAdmin.objects.select_related('auth_user').get(
            account__accountlegalentity__legal_entity=legal_entity, auth_user=user).auth_user

        address_dto = AddressDTO(data=dict(
            firstname=auth_user.first_name,
            lastname=auth_user.last_name,
            email=auth_user.email,
            telephone=settings.QWIKCILVER_CONF['DEFAULT_TREEBO_PHONENUMBER'],
            line_1="AMR Tech Park",
            line_2="Block 1 , First Floor,",
            city="Bengaluru",
            region="Karnataka",
            country_id="IN",
            postcode="560068"
        ))

        if not address_dto.is_valid():
            logger.info("invalid address_dto. Error {err}".format(err=address_dto.errors))
            msg = "[QwikcilverGiftCardOrder] _get_corp_details : Invalid DTO : errors : {errors}".format(
                errors=address_dto.errors)
            send_slack_notif(constants.Slack.B2B_APP_ALERTS, msg, raise_exception=False)
            raise InvalidDTO(address_dto)
        return address_dto.data

    @classmethod
    def _get_qs_products(cls, order_data):
        qs_product_list = []
        products = order_data['products']
        for product_info in products:
            for item in product_info['product_items']:
                qs_product = cls._catalog_item_to_qs_product(item)
                qs_product_list.append(qs_product)
        return qs_product_list

    @classmethod
    def _catalog_item_to_qs_product(cls, item):
        item_id = item['item_id']
        try:
            catalog_item = TreeboCatelogItem.objects.get(id=item_id)
        except TreeboCatelogItem.DoesNotExist:
            raise InvalidCatalogItem(item_id)

        try:
            treebo_catalog_product = TreeboCatelogProduct.objects.get(id=catalog_item.product_id)
        except TreeboCatelogProduct.DoesNotExist:
            raise CatalogProductDoesNotExist(catalog_item.product_id)

        try:
            qwikcilver_product = QwikcilverProducts.objects.get(treebo_catelog_product_id=treebo_catalog_product.id,
                                                                active=True)
        except QwikcilverProducts.DoesNotExist:
            raise QwikcilverProductDoesntExist(item_id)

        qs_product_id = qwikcilver_product.qs_product_id
        points = catalog_item.points

        price_valid = cls._check_price_validity_against_qwikcilver_product_info(qwikcilver_product, points)

        if not price_valid:
            raise InvalidQwikcilverItemPrice(qs_product_id, points)

        product = ProductDTO(data=dict(
            product_id=qs_product_id,
            price=points,
            qty=item['item_quantity']
        ))

        if product.is_valid():
            return product.data

    @classmethod
    def _check_price_validity_against_qwikcilver_product_info(cls, qs_product, price):
        result = True
        if qs_product:
            min_custom_price = int(qs_product.min_custom_price)
            max_custom_price = int(qs_product.max_custom_price)
            price_type = qs_product.price_type

            if price_type == 'slab':
                custom_denominations = list(map(int, qs_product.custom_denominations.split(",")))
                # in this case, the price should be an exact match for the custom denominations, or the min/max custom prices
                custom_denominations.append(min_custom_price)
                custom_denominations.append(max_custom_price)

                if price not in custom_denominations:
                    result = False

            elif price_type == 'range':
                # just check that the price is inside the min and max prices
                if price < min_custom_price or price > max_custom_price:
                    result = False
        else:
            result = False

        return result

    @classmethod
    def _get_reference_number(cls):
        return "Treebo-RefNo-" + str(uuid.uuid4())

    @classmethod
    def _add_qs_order_to_database(cls, request_data, order_id):
        logger.debug("Adding Qwikcviler Order to database, order_id={order_id}, request_data={request_data}"
                     .format(order_id=order_id, request_data=request_data))
        loyalty_order_row = LoyaltyOrder.objects.get(order_id=order_id)
        qs_order = QwikcilverOrders(loyalty_order=loyalty_order_row,
                                    address=json.dumps(request_data['billing']),
                                    ref_no=request_data['refno'],
                                    redeem_amount=request_data['payment_method'][0]['amount_to_redem'],
                                    purchase_order=request_data['payment_method'][0]['po_number'],
                                    products=json.dumps(request_data['products']),
                                    process_status='processing',
                                    )
        qs_order.save()
        return qs_order.id

    @classmethod
    def _update_qs_order(cls, qs_order_id, response):
        logger.debug(
            "Updating qwikcilver_order={qs_order_id}, data={data}".format(qs_order_id=qs_order_id, data=response))
        response_json = response.json()
        if response_json and type(response_json) is list:
            response_json = response_json[0]

        qs_order = QwikcilverOrders.objects.get(pk=qs_order_id)
        qs_order.process_status = 'processed'
        qs_order.success = response_json.get('success', False)
        qs_order.order_status = response_json.get('status', "")
        qs_order.message = response_json.get('message', "")
        qs_order.qs_order_id = response_json.get('order_id', "")
        qs_order.carddetails = json.dumps(response_json.get('carddetails', ""))
        qs_order.save(
            update_fields=['process_status', 'order_status', 'success', 'message', 'qs_order_id', 'carddetails'])
