import json
import logging
from django.conf import settings

from loyalty.consumer.catelog_config.catelog_config_interface import BaseCatelogConfig
from loyalty.models.order_config import OrderConfig
from qwikcilver.client import QwikcilverClient
from loyalty.consumer.gift_card_order.qwikcilver_gift_card_order.b2b_qwikcilver_authstore import B2BQwikcilverAutoStore
from loyalty.dto.order_config_dto import OrderConfigDTO
from loyalty.exceptions import QwikcilverSettingMissing

logger = logging.getLogger(__name__)


class QwikcilverConfig(BaseCatelogConfig):
    NAMESPACE = 'qwikcilver'

    def get_config(self):
        configs = {config.key: config.value for config in
                       OrderConfig.objects.filter(namespace=self.NAMESPACE)}

        order_config_dto = OrderConfigDTO(data=configs)
        if order_config_dto.is_valid():
            return order_config_dto
        else:
            raise QwikcilverSettingMissing(list(order_config_dto.errors.keys()))


    @classmethod
    def update_config(cls):
        logger.info("Fetching Quicksilver Settings")
        auth_store = B2BQwikcilverAutoStore()
        client = QwikcilverClient(qwikcilver_config=settings.QWIKCILVER_CONF, auth_store=auth_store)
        response = client.get_settings()

        if response.status_code == 200:
            config_dict = cls.parse_settings_response(response.json())
            cls.update_config_db(config_dict)

    @classmethod
    def parse_settings_response(cls, settings_object):
        store_id = settings_object['store_id']
        product_max_qty_allow = settings_object['config']['product_max_qty_allow']
        max_item_in_cart = settings_object['config']['max_item_in_cart']

        return dict(store_id=store_id,
                    product_max_qty_allow=product_max_qty_allow,
                    max_item_in_cart=max_item_in_cart
                    )

    @classmethod
    def update_config_db(cls, config_dict):
        logger.info("Loyalty order config db is being updated with data {data}".format(data=json.dumps(config_dict)))
        for key, value in list(config_dict.items()):
            row = OrderConfig.objects.filter(namespace=cls.NAMESPACE, key=key)
            if not row:
                data = {"namespace" : cls.NAMESPACE, "key" : key, "value" : value}
                OrderConfig.objects.create(**data)
            else:
                row[0].value = value
                row[0].save()
