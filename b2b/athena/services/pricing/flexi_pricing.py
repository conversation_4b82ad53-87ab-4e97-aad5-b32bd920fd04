from decimal import Decimal

from athena.dto.flexi_pricing_request import FlexiPricingDTO
from athena.dto.flexi_pricing_response import FlexiPricingResponseDTO
from athena.models import DefaultFlexiPrice
from athena.models import FlexiPrice
from b2b import constants
from b2b.domain.services import PricingService
from b2b.dto import EPChargeRequestDTO
from b2b.domain.pricing.exceptions import NoMatchesFound


class FlexiPricing(object):
    def __init__(self, pricing_dto):
        assert type(pricing_dto) is FlexiPricingDTO
        if not pricing_dto.is_valid():
            raise Exception("Pricing DTO is invalid due to {errs}".format(errs=pricing_dto.errors))
        pricing_dto = pricing_dto.data
        self.legal_entity_id = pricing_dto['legal_entity_id']
        self.hotel = pricing_dto['hotel']
        self.check_in = pricing_dto['check_in']
        self.check_out = pricing_dto['check_out']
        self._pricing_service = PricingService(legal_entity_id=self.legal_entity_id)

    def get_min_prices(self):
        min_prices = []
        default_min_prices = self._get_default_min_prices()
        custom_min_prices = self._get_custom_min_prices()
        for room_type in constants.Rooms.TYPES:
            room_type = room_type[0]
            ep_charges = self.__override_extra_person_charges(room_type)
            price = custom_min_prices.filter(room_type=room_type)
            if not price:
                price = default_min_prices.filter(room_type=room_type)

            if price:
                for m_price in price:
                    min_prices.append(dict(price=Decimal(m_price.room_price),
                                           room_type=room_type,
                                           ep_charge=ep_charges,
                                           reason=m_price.reason))
        return FlexiPricingResponseDTO(data=dict(prices=min_prices))

    def _get_default_min_prices(self):
        return DefaultFlexiPrice.objects.all()

    def _get_custom_min_prices(self):
        return FlexiPrice.objects.filter(hotel__hotel_id=self.hotel,
                                         start_date__lte=self.check_in,
                                         end_date__gte=self.check_out)

    def __override_extra_person_charges(self,room_type):
        return self.__get_std_ep_charges(room_type)

    def __get_std_ep_charges(self,room_type):
        ep_charge_request_dto = EPChargeRequestDTO(data=dict(legal_entity_id=self.legal_entity_id,
                                                             hotel=self.hotel,
                                                             from_date=self.check_in,
                                                             to_date=self.check_out,
                                                             room_type=room_type,
                                                             room_config='2-0'))

        try:
            ep_charges_dto = self._pricing_service.get_extra_person_charges(ep_charge_request_dto)
            if not ep_charges_dto.is_valid():
                # TODO: Segregate Exception Types
                raise Exception('Invalid data received from Pricing Service for Extra person charges')
            return Decimal(ep_charges_dto.data['pre_tax_charge'])
        except NoMatchesFound:
            return 0