# -*- coding: utf-8 -*-
# pylint: disable=too-many-locals,old-division

import json
import logging
from decimal import Decimal

from django.conf import settings
from django.urls import reverse
from django.http import HttpResponseRedirect
from rest_framework import status
from rest_framework.renderers import TemplateHTMLRenderer
from rest_framework.response import Response

from athena.api.api_error_response import APIErrorResponse
from athena.api.base import AthenaAPI
from athena.dto.booking_detail import BookingDetailRequestDTO, BookingDetailResponseDTO, BookingMetaDataDTO
from athena.services.booking import BookingService as AthenaBookingService
from b2b import constants
from b2b.api.renderer import <PERSON>boJSONRenderer
from b2b.api.request_dtos import CancelBookingRequest, SoftBookingRequestDTO
from b2b.consumer.crs.utils import convert_to_local_timezone
from b2b.domain.services import BookingService
from b2b.domain.services.exceptions import BookingAlreadyCancelledException
from b2b.domain.services.exceptions import TooLateToCancelException, InvalidBookingStatusForCancellation
from b2b.domain.utils.date_utils import count_room_nights
from b2b.dto.booking_status import BookingStatusDTO
from b2b.models import Booking


class EditBookingView(AthenaAPI):
    renderer_classes = (TreeboJSONRenderer, TemplateHTMLRenderer)

    def get(self, request, *args, **kwargs):
        """
        gives all details of a single booking
        """
        logger = logging.getLogger(self.__class__.__name__)
        req_serializer = BookingDetailRequestDTO(data=kwargs)
        if not req_serializer.is_valid():
            return HttpResponseRedirect(reverse('athena:landing'))
        booking_id = req_serializer.validated_data['booking_id']
        booking = Booking.objects.get(booking_id=booking_id)

        if booking.crs_type == constants.Booking.THSC:
            return HttpResponseRedirect('{base}?booking_id={b}'.format(base=reverse('athena:v2:landing'), b=booking_id))

        room_nights = count_room_nights(str(booking.check_in), str(booking.check_out))

        hotel_details = AthenaBookingService.get_hotel_details(booking.hotel.hotel_id)
        occ_details = AthenaBookingService.build_max_occupancy(hotel_details)

        min_prices = AthenaBookingService.get_flexi_min_prices(booking)

        ep_charges = AthenaBookingService.build_ep_dto(booking, min_prices)

        meta_data_dto = BookingMetaDataDTO(
            data={'occupancy': occ_details, 'sync_with_hx': AthenaBookingService.sync_hx_status(booking),
                  'ep_charge': ep_charges})

        if not meta_data_dto.is_valid():
            logger.info('Meta data errors:  {errors}'.format(
                errors=meta_data_dto.errors))
            return APIErrorResponse.error_response(message=meta_data_dto.errors,
                                                   resp_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        resp_serializer = BookingDetailResponseDTO(booking)
        try:
            resp_serializer_data = resp_serializer.data
            for room in resp_serializer_data['rooms']:
                room['price'] = str(round(Decimal(room['price']) / room_nights, 2))
                room['pre_tax_price'] = str(round(Decimal(room['pre_tax_price']) / room_nights, 2))
                room['meal']['veg']['quantity'] = room['meal']['veg']['quantity'] / room_nights
                room['meal']['non_veg']['quantity'] = room['meal']['non_veg']['quantity'] / room_nights

            legal_entity = BookingService.get_legal_entity(booking=booking)

            previous_booking_id = booking.get_attribute(constants.BookingAttributes.PREVIOUS_BOOKING_ID)

            room_night_discount_per = booking.get_attribute(constants.BookingAttributes.ROOM_NIGHT_DISCOUNT_PERCENTAGE)

            resp_serializer_data['legal_entity'] = legal_entity
            soft_block_expiry_time = resp_serializer_data['soft_block_expiry_time']
            if soft_block_expiry_time:
                resp_serializer_data['soft_block_expiry_time'] = convert_to_local_timezone(soft_block_expiry_time)

            if previous_booking_id:
                resp_serializer_data['old_booking_id'] = previous_booking_id

            resp_serializer_data['room_night_discount_per'] = room_night_discount_per
            data = json.dumps(resp_serializer_data)
            meta_data = json.dumps(meta_data_dto.data)
        except Exception as e:
            logger.info('Error while getting booking details on athena : {id}, error : {err}'.format(
                id=booking_id,
                err=str(e)))
            return HttpResponseRedirect(reverse('athena:landing'))

        return Response(data={'data': data, 'meta_data': meta_data, 'booking_block_view': True,
                              'gstin_enabled': True, 'max_booking_period': settings.MAX_BOOKING_PERIOD},
                        template_name='athena/corporate_booking.html')

    def delete(self, request, *args, **kwargs):
        """
        cancels a booking
        :param request: data contains booking-id in json
        """
        logger = logging.getLogger(self.__class__.__name__)

        dto = CancelBookingRequest(data=kwargs)
        if not dto.is_valid():
            return Response(dto.errors, status.HTTP_400_BAD_REQUEST)

        booking_id = dto.data['booking_id']

        try:
            BookingService.cancel_booking(booking_id)

        except Booking.DoesNotExist as e:
            return APIErrorResponse.booking_does_not_exist(booking_id=booking_id)

        except (TooLateToCancelException,
                BookingAlreadyCancelledException,
                InvalidBookingStatusForCancellation) as e:
            # for now we just use the exception's own message as the error message
            # 410 GONE because that's the closest error code that indicates cancellation can't
            # happen anymore for this booking id, and shouldn't be re-tried either (since
            # if it's too late now, it will be too late any time after now anyway)
            return APIErrorResponse.error_response(message=str(e),
                                                   resp_code=status.HTTP_410_GONE)

        except Exception as e:  # NOQA
            logger.info("Error cancelling booking %s: %s", str(e), booking_id)
            raise

        response = Response(data={'message': 'Booking {b} cancelled'.format(b=booking_id)},
                            status=status.HTTP_200_OK)

        return response

    def post(self, request, *args, **kwargs):
        logger = logging.getLogger(self.__class__.__name__)

        dto = SoftBookingRequestDTO(data=kwargs)
        if not dto.is_valid():
            return Response(dto.errors, status.HTTP_400_BAD_REQUEST)

        try:
            booking_status_dto = BookingService.confirm_soft_booking(dto)

            assert type(booking_status_dto) is BookingStatusDTO  # pylint: disable=unidiomatic-typecheck

            if not booking_status_dto.is_valid():
                raise RuntimeError('Invalid soft booking status DTO: {err}'.format(err=booking_status_dto.errors))

        except Exception as e:
            logger.info("error occurred while confirming soft booking: %s", str(e))
            return APIErrorResponse.soft_booking_request_failed(booking_id=dto.data['booking_id'])

        response = Response(data=booking_status_dto.data,
                            status=status.HTTP_201_CREATED)
        return response
