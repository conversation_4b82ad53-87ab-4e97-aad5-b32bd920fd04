from django.conf import settings
from rest_framework import serializers

from b2b import constants


class AvailibilityAndPricingRequestDTO(serializers.Serializer):
    legal_entity_id = serializers.CharField(max_length=20)
    hotel = serializers.CharField(max_length=20)
    check_in = serializers.DateField(format=settings.BOOKING['date_format'])
    check_out = serializers.DateField(format=settings.BOOKING['date_format'])
    room_config = serializers.CharField(max_length=20, required=False)
    room_type = serializers.CharField(max_length=20, required=False)
    subchannel = serializers.Char<PERSON>ield(max_length=50, required=False, default=constants.Booking.DIRECT)

    def validate(self, data):
        # verify check-in check-out dates are valid
        if data['check_in'] > data['check_out']:
            raise serializers.ValidationError("check-in date should be before check-out date")

        if data['check_in'] == data['check_out']:
            raise serializers.ValidationError("check-in and check-out cannot be on the same day")

        return data
