import json
import logging
import requests

from b2b.constants import URL_SHORTENING_API

logger = logging.getLogger(__name__)


def shorten_url(url):
    try:
        link_request = {
            "long_url": url,
        }

        request_headers = {
            "Content-Type": "application/json",
        }

        response = requests.post(URL_SHORTENING_API,
                                 data=json.dumps(link_request),
                                 headers=request_headers)

        if response.status_code in (requests.codes.ok, requests.codes.created, requests.codes.no_content):
            link = response.json()
            return link["short_url"], link["short_code"]

        raise Exception(f'Invalid status received in api response {response.status_code}')

    except Exception as ex:
        error_message = "Exception: {} Occurred during url shortening".format(ex)
        logger.info(error_message)
        logger.info(ex)
