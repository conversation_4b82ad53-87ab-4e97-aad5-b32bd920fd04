from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('b2b', '0022_auto_20170306_1430'),
    ]

    operations = [
        migrations.CreateModel(
            name='DefaultFlexiPrice',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('room_type', models.CharField(max_length=50, choices=[(b'Acacia', b'Acacia'), (b'Maple', b'Maple'), (b'Oak', b'Oak'), (b'Mahogany', b'Mahogany')])),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('room_price', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
                ('epc_price', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
                ('reason', models.CharField(default=b'', max_length=200, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FlexiPrice',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Modified at')),
                ('room_type', models.CharField(max_length=50, choices=[(b'Acacia', b'Acacia'), (b'Maple', b'Maple'), (b'Oak', b'Oak'), (b'Mahogany', b'Mahogany')])),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('room_price', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
                ('extra_person_charges', models.DecimalField(default=0, max_digits=20, decimal_places=2)),
                ('reason', models.CharField(default=b'', max_length=200, null=True)),
                ('hotel', models.ForeignKey(to='b2b.Hotel', on_delete=models.CASCADE)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
