# -*- coding: utf-8 -*-

import logging

from rest_framework import status
from rest_framework.response import Response

from athena.api.base import AthenaV2API
from athena.services.booking import BookingService
from athena.utils.booking import (expand_booking_gst_details_as_corporate_legal_entity,
                                  add_gst_details_to_legal_entity_and_mark_as_default)
from athena.v2.booking_converter import BookingConverter
from athena.v2.serializers.booking import BookingSerializer
from core.booking.aggregates.trinity import Trinity
from core.booking.providers.get_booking.treebo import TreeboCRSGetBooking
from core.booking.providers.update_booking.treebo import TreeboCRSUpdateBooking
from core.notification import notify
from core.notification.handlers.booking.updated import BookingUpdatedHandler

logger = logging.getLogger(__name__)


class BookingAPI(AthenaV2API):

    def get(self, request, booking_id):
        booking = TreeboCRSGetBooking(booking_id=booking_id).get()
        trinity_booking = Trinity(booking=booking)

        booking_serializer = BookingSerializer(trinity_booking)
        return Response(
            data=booking_serializer.data,
            status=status.HTTP_200_OK,
        )

    def delete(self, request, booking_id):
        BookingService.cancel_booking(booking_id)
        return Response(
            data={'message': 'Booking {b} cancelled'.format(b=booking_id)},
            status=status.HTTP_200_OK,
        )

    def put(self, request, booking_id):
        serializer = BookingSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        new_booking = BookingConverter(booking_data=serializer.data).convert()
        current_booking = TreeboCRSGetBooking(booking_id=booking_id).get()
        updated_booking = TreeboCRSUpdateBooking(booking=current_booking).update(booking=new_booking)

        if not updated_booking.expiry_time_stamp:
            email = BookingUpdatedHandler(updated_booking).email()
            notify(email)

        trinity_booking = Trinity(booking=updated_booking)

        booking_serializer = BookingSerializer(trinity_booking)

        return Response(
            data=booking_serializer.data,
            status=status.HTTP_200_OK,
        )
