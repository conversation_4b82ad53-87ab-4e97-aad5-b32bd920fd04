# -*- coding: utf-8 -*-
from django.test import SimpleTestCase
from b2b.domain.pricing.room_pricing_strategy import RPSv2
from mock import patch, Mock
from b2b.models import Hotel
from b2b.models import Corporate
from b2b.domain.pricing.exceptions import NoMatchesFound, PriceNotAvailable


class TestRPSv2(SimpleTestCase):
    def setUp(self):
        self.corporate = Mock(spec=Corporate, id=1, corporate_id=1)
        self.hotel = Mock(spec=Hotel, id=1, hotel_id= 1)
        self.standard_ep_charges = dict(
            new_pricing_scheme=True,
            hotel_id=self.hotel.id,
            pre_tax_price=100
        )

    @patch('b2b.domain.pricing.room_pricing_strategy.rps_v2.CorpHotelRoomTypeSelectorV2')
    @patch('b2b.domain.pricing.room_pricing_strategy.rps_v2.HotelRoomTypeSelectorV2')
    @patch('b2b.domain.pricing.room_pricing_strategy.rps_v2.DefaultRoomPriceSelector')
    def test_get_price_from_available_selector(self, mock_selector, mock_selector_rt, mock_selector_ch):
        mock_selector.return_value.get_room_price.return_value = 1
        mock_selector_rt.return_value.get_room_price.side_effect = NoMatchesFound(mock_selector_rt)
        mock_selector_ch.return_value.get_room_price.side_effect = NoMatchesFound(mock_selector_ch)
        rps = RPSv2()
        rr = rps.get_room_rate(self.corporate,
                               self.hotel,
                               '1', '2017-01-01', '2017-01-01',
                               [self.standard_ep_charges], [])
        self.assertEqual(rr, 1)
