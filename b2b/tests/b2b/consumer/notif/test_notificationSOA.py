# -*- coding: utf-8 -*-
from django.test import Simple<PERSON>est<PERSON><PERSON>
from mock import patch, Mock

from b2b.dto import EmailDTO
from b2b.consumer.notif.notif_soa import NotificationSOA


class TestNotificationSOA(SimpleTestCase):

    def test_email_dto_with_multiple_email_addrs(self):
        """
        it should correctly load the email-dto when there are multiple
        email addresses are specified in to/cc/bcc
        """
        to_list = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'.split(',')
        cc_list = ['<EMAIL>']
        bcc_list = ['<EMAIL>', '<EMAIL>']

        email_dto = EmailDTO(data=dict(sender='<EMAIL>',
                                       to_list=to_list,
                                       subject="test subject",
                                       content="test content",
                                       cc_list=cc_list,
                                       bcc_list=bcc_list))

        if not email_dto.is_valid():
            print(("invalid email dto: {err}".format(err=email_dto.errors)))

        self.assertTrue(email_dto.is_valid())

    def test_email_dto_without_cc_and_bcc(self):
        """
        if cc and bcc are not specified, we don't care
        email addresses are specified in to/cc/bcc
        """
        to_list = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'.split(',')

        email_dto = EmailDTO(data=dict(sender='<EMAIL>',
                                       to_list=to_list,
                                       subject="test subject",
                                       content="test content"))

        if not email_dto.is_valid():
            print(("invalid email dto: {err}".format(err=email_dto.errors)))

        self.assertTrue(email_dto.is_valid())

        self.assertTrue(type(email_dto.data['cc_list']) is list)
        self.assertEqual(0, len(email_dto.data['cc_list']))
        self.assertTrue(type(email_dto.data['bcc_list']) is list)
        self.assertEqual(0, len(email_dto.data['bcc_list']))

    @patch('b2b.consumer.notif.notif_soa.requests')
    def test_send_email(self, mock_requests):
        to_list = '<EMAIL>,<EMAIL>'.split(',')

        class MockResponse(object):
            def __init__(self, response_text=''):
                self.text = response_text

            def json(self):
                return {'data': {'status': 'success',
                                 'data': {'notification_id': '1234556'}}}

        mock_requests.post.return_value = MockResponse()

        email_dto = EmailDTO(data=dict(sender="<EMAIL>",
                                       to_list=to_list,
                                       subject='Test Email - NofiSOA::SendEmail',
                                       content='Testing Content'))

        email_backend = NotificationSOA()
        email_backend.send_email(email_dto)

        self.assertTrue(mock_requests.post.called)

    def __test_send_email_for_real(self):
        """
        only use when testing the actual notification service
        just remove the underscore, and the test will run as part of the suite
        """
        to_list = ['<EMAIL>', '<EMAIL>']

        email_dto = EmailDTO(data=dict(sender="<EMAIL>",
                                       to_list=to_list,
                                       subject='Test Email - NofiSOA::SendEmail',
                                       content='Testing Content'))

        email_backend = NotificationSOA()
        email_backend.send_email(email_dto)
